{"system": "AI Image Generator", "helloWorld": "こんにちは、世界！", "Describe the image you want to generate...": "生成したい画像を説明してください...", "appTitle": "AI画像生成", "copyright": "Copyright © {year} GeminiGen.AI", "available": "新しいプロジェクトに利用可能", "notAvailable": "現在利用できません。", "blog": "ブログ", "copyLink": "リンクをコピー", "minRead": "短時間読了", "articleLinkCopied": "記事リンクをクリップボードにコピーしました", "clickToClose": "どこでもクリックするかESCを押して閉じてください。", "promptDetails": "プロンプトの詳細", "generateWithPrompt": "このプロンプトで生成する", "generateWithSettings": "これらの設定で生成する", "preset": "プリセット", "style": "スタイル", "resolution": "解像度", "addImage": "画像を追加", "modelPreset": "モデル/プリセット", "imageDimensions": "画像の寸法", "yourImage": "あなたの画像", "generate": "生成", "nav.aitool": "AIツール", "nav.api": "API", "nav.login": "ログイン", "nav.history": "歴史", "nav.orders": "注文", "3D Render": "3Dレンダー", "Acrylic": "アクリル", "Anime General": "アニメ総合", "Creative": "クリエイティブ", "Dynamic": "ダイナミック", "Fashion": "ファッション", "Game Concept": "ゲームコンセプト", "Graphic Design 3D": "グラフィックデザイン3D", "Illustration": "イラストレーション", "None": "なし", "Portrait": "ポートレート", "Portrait Cinematic": "ポートレートシネマティック", "Portrait Fashion": "ポートレートファッション", "Ray Traced": "レイトレーシング", "Stock Photo": "ストックフォト", "Watercolor": "水彩画", "AI Image Generator": "AI画像ジェネレーター", "Generate AI images from text prompts with a magical particle transformation effect": "テキストプロンプトから魔法のような粒子変換効果でAI画像を生成", "Enter your prompt": "プロンプトを入力してください", "Generating...": "生成中...", "Generate Image": "画像を生成", "Enter a prompt and click Generate Image to create an AI image": "プロンプトを入力し、「画像を生成」をクリックしてAI画像を作成します", "Prompt:": "プロンプト：", "Download": "ダウンロード", "How It Works": "仕組み", "This AI image generator uses a particle-based transformation effect to visualize the creation process. When you enter a prompt and click 'Generate', the system:": "このAI画像ジェネレーターは、粒子ベースの変換効果を使用して作成プロセスを視覚化します。プロンプトを入力して「生成」をクリックすると、システムは：", "Sends your prompt to an AI image generation API": "あなたのプロンプトをAI画像生成APIに送信します", "Creates a particle system with thousands of tiny particles": "何千もの小さな粒子でパーティクルシステムを作成します", "Transforms the random noise particles into the generated image": "ランダムなノイズ粒子を生成された画像に変換します", "The particles start in a random noise pattern and then smoothly transform into the final image, creating a magical effect that simulates the AI's creative process.": "粒子はランダムなノイズパターンから始まり、最終的な画像にスムーズに変換され、AIの創造的なプロセスをシミュレートする魔法のような効果を生み出します。", "Transform": "変換", "Transforming...": "変換中...", "Initializing particles...": "粒子を初期化中...", "Loading image...": "画像を読み込み中...", "Creating particle system...": "パーティクルシステムを作成中...", "Adding event listeners...": "イベントリスナーを追加中...", "Ready!": "準備完了！", "AI Image Particle Effect": "AI画像パーティクル効果", "A demonstration of the BaseMagicImage component that transforms particles into AI-generated images": "粒子をAI生成画像に変換するBaseMagicImageコンポーネントのデモンストレーション", "Click anywhere or press ESC to close": "どこでもクリックするかESCを押して閉じる", "auth.login": "ログイン", "auth.loginDescription": "続行するにはアカウントにログインしてください", "auth.email": "メール", "auth.enterEmail": "メールアドレスを入力してください", "auth.password": "パスワード", "auth.enterPassword": "パスワードを入力してください", "auth.rememberMe": "ログイン情報を記憶する", "auth.welcomeBack": "おかえりなさい", "auth.dontHaveAccount": "アカウントをお持ちでないですか？", "auth.signUp": "サインアップ", "auth.forgotPassword": "パスワードをお忘れですか？", "auth.signupFailed": "サインアップに失敗しました", "auth.signupFailedDescription": "サインアップ中にエラーが発生しました。もう一度お試しください。", "auth.bySigningIn": "ログインすることで、当社の", "auth.termsOfService": "利用規約に同意したことになります", "auth.signUpTitle": "サインアップ", "auth.signUpDescription": "始めるためにアカウントを作成してください", "auth.name": "名前", "auth.enterName": "お名前を入力してください", "auth.createAccount": "アカウントを作成", "auth.alreadyHaveAccount": "すでにアカウントをお持ちですか？", "auth.bySigningUp": "サインアップすることで、当社の", "auth.backToHome": "ホームに戻る", "auth.notVerifyAccount": "アカウントが確認されていません。続行するにはアカウントを確認してください", "auth.verifyAccount": "アカウントを確認", "auth.resendActivationEmail": "アクティベーションメールを再送信", "auth.accountRecovery": "アカウント復旧", "auth.accountRecoveryTitle": "アカウントを復旧する", "auth.accountRecoveryDescription": "パスワードリセットの手順を受け取るためにメールアドレスを入力してください", "auth.sendRecoveryEmail": "復旧メールを送信", "auth.recoveryEmailSent": "復旧メールが送信されました", "auth.recoveryEmailSentDescription": "パスワードリセットの手順についてメールをご確認ください", "auth.resetPassword": "パスワードリセット", "auth.resetPasswordTitle": "パスワードをリセット", "auth.resetPasswordDescription": "新しいパスワードを入力してください", "auth.newPassword": "新しいパスワード", "auth.confirmPassword": "パスワード確認", "auth.enterNewPassword": "新しいパスワードを入力してください", "auth.enterConfirmPassword": "新しいパスワードを確認してください", "auth.passwordResetSuccess": "パスワードリセット成功", "auth.passwordResetSuccessDescription": "パスワードが正常にリセットされました。新しいパスワードでログインできます", "auth.activateAccount": "アカウント有効化", "auth.activateAccountTitle": "アカウントを有効化", "auth.activateAccountDescription": "アカウントを有効化しています...", "auth.accountActivated": "アカウントが有効化されました", "auth.accountActivatedDescription": "アカウントが正常に有効化されました。ログインできます", "auth.activationFailed": "有効化に失敗しました", "auth.activationFailedDescription": "アカウントの有効化に失敗しました。再試行するかサポートにお問い合わせください", "auth.backToLogin": "ログインに戻る", "auth.loginFailed": "ログインに失敗しました", "auth.loginWithGoogle": "Googleでログイン", "auth.google": "Google", "auth.filter": "Filter", "validation.invalidEmail": "無効なメールアドレス", "validation.passwordMinLength": "パスワードは8文字以上である必要があります", "validation.nameRequired": "名前は必須です", "validation.required": "この項目は必須です", "validation.passwordsDoNotMatch": "パスワードが一致しません", "imageSelect.pleaseSelectImageFile": "画像ファイルを選択してください", "imageSelect.selectedImage": "選択された画像", "imageSelect.removeImage": "画像を削除", "pixelReveal.loading": "画像を読み込み中...", "pixelReveal.processing": "画像を処理中...", "pixelReveal.revealComplete": "画像の表示が完了しました", "SIGNIN_WRONG_EMAIL_PASSWORD": "メールアドレスまたはパスワードが間違っています", "Try again": "再試行", "aiToolMenu.imagen": "画像生成", "aiToolMenu.videoGen": "動画生成", "aiToolMenu.speechGen": "音声生成", "aiToolMenu.musicGen": "音楽生成", "aiToolMenu.imagen3": "Imagen 3", "aiToolMenu.imagen3Description": "クリエイティブな視覚コンテンツのための正確なテキストレンダリングを備えた高品質で詳細な画像を生成します。", "aiToolMenu.imagen4": "Imagen 4", "aiToolMenu.imagen4Description": "かつてないほどあなたのアイデアを表現してください — Imagenを使えば、創造性に限界はありません。", "aiToolMenu.gemini2Flash": "Gemini 2.0 Flash", "aiToolMenu.gemini2FlashDescription": "Gemini 2.0 Flashはテキストプロンプトから画像を生成する強力なツールです。", "aiToolMenu.veo2": "Veo 2", "aiToolMenu.veo2Description": "かつてないほどの制御力、一貫性、創造性。", "aiToolMenu.veo3": "Veo 3", "aiToolMenu.veo3Description": "動画がオーディオと出会う。映画制作者やストーリーテラーに力を与えるために設計された、私たちの最新の動画生成モデル。", "aiToolMenu.gemini25Pro": "Gemini 2.5 Pro", "aiToolMenu.gemini25ProDescription": "利用可能な最も先進的なテキスト読み上げモデル。", "aiToolMenu.gemini25Flash": "Gemini 2.5 Flash", "aiToolMenu.gemini25FlashDescription": "大規模処理（例：複数のPDF）。\n思考を必要とする低遅延、大容量タスク\nエージェンティックユースケース", "aiToolMenu.link": "リンク", "aiToolMenu.linkDescription": "スーパーパワーを持つNuxtLinkを使用。", "aiToolMenu.soon": "近日公開", "readArticle": "記事を読む", "switchToLightMode": "ライトモードに切り替え", "switchToDarkMode": "ダークモードに切り替え", "profile": "プロフィール", "buyCredits.checkout": "チェックアウト", "buyCredits.checkoutDescription": "注文を確認してから、お支払い方法を選択してください。", "buyCredits.orderDetail": "注文詳細", "buyCredits.credits": "クレジット", "buyCredits.pricePerUnit": "単価", "buyCredits.totalCredits": "総クレジット", "buyCredits.totalPrice": "総価格", "buyCredits.payment": "お支払い", "buyCredits.submit": "注文する", "buyCredits.cancel": "キャンセル", "pricing.title": "料金", "pricing.description": "画像生成ニーズに最適なプランをお選びください", "pricing.comingSoon": "近日公開", "pricing.comingSoonDescription": "料金プランを最終調整中です。更新情報については後ほどご確認ください。", "magicImageDemo.title": "AI画像パーティクル効果", "magicImageDemo.description": "パーティクルをAI生成画像に変換するBaseMagicImageコンポーネントのデモンストレーション", "magicImageDemo.image": "画像", "magicImageDemo.aboutTitle": "このコンポーネントについて", "magicImageDemo.aboutDescription": "BaseMagicImageコンポーネントはThree.jsを使用して、ランダムな位置とAI生成画像の間で変換できるパーティクルシステムを作成します。パーティクルは渦巻きと流れの効果で移動し、魔法のような変換を作り出します。", "magicImageDemo.featuresTitle": "機能", "magicImageDemo.features.particleRendering": "パーティクルベースの画像レンダリング", "magicImageDemo.features.smoothTransitions": "ランダムなパーティクル位置と画像形成間のスムーズな移行", "magicImageDemo.features.interactiveControls": "インタラクティブなカメラコントロール（ドラッグで回転、スクロールでズーム）", "magicImageDemo.features.customizable": "カスタマイズ可能なパーティクル数とアニメーション時間", "magicImageDemo.features.automatic": "自動または手動変換トリガー", "magicImageDemo.howItWorksTitle": "動作原理", "magicImageDemo.howItWorksDescription": "このコンポーネントは画像のピクセルを分析し、各パーティクルがピクセルを表す3Dパーティクルシステムを作成します。明るいピクセルは視聴者により近く配置され、微妙な3D効果を作り出します。パーティクルは最初3D空間にランダムに散らばり、トリガーされると画像を形成するようにアニメーションします。", "privacy.title": "プライバシーポリシー", "privacy.description": "プライバシーの保護とデータの取り扱いについて説明します", "privacy.informationWeCollect": "収集する情報", "privacy.dataSecurity": "データセキュリティ", "privacy.dataSecurityDescription": "お客様の個人情報を不正なアクセス、変更、開示、または破壊から保護するため、適切なセキュリティ対策を実施しています。", "privacy.contactUs": "お問い合わせ", "terms.title": "利用規約", "terms.description": "Imagenサービスの利用規約と条件", "terms.acceptanceOfTerms": "1. 規約の同意", "terms.acceptanceOfTermsDescription": "Imagenサービスにアクセスして使用することで、本契約の条項と条件に拘束されることに同意し、同意します。", "terms.useOfService": "2. サービスの使用", "terms.userAccounts": "3. ユーザーアカウント", "terms.userAccountsDescription": "お客様は、アカウントとパスワードの機密性を維持する責任があります。", "terms.intellectualProperty": "4. 知的財産", "terms.intellectualPropertyDescription": "当社のサービスで利用可能なすべてのコンテンツと資料は、知的財産権によって保護されています。", "terms.termination": "5. 終了", "terms.terminationDescription": "当社は、独自の裁量でお客様のアカウントとサービスへのアクセスを終了または停止する場合があります。", "terms.disclaimers": "6. 免責事項", "terms.disclaimersDescription": "サービスは、いかなる種類の保証もなく「現状のまま」提供されます。", "terms.contactUsTerms": "お問い合わせ", "terms.contactUsTermsDescription": "これらの利用規約についてご質問がございましたら、サポートチャンネルを通じてお問い合わせください。", "Describe the video you want to generate...": "生成したいビデオを説明してください。", "cancel": "キャンセル", "confirm": "確認", "appName": "GeminiGen.AI", "quickTopUp": "クイックチャージ", "customTopUp": "カスタムチャージ", "numberOfCredits": "単位数", "paypal": "ペイパル", "paypalDescription": "PayPalアカウントで安全に支払う", "debitCreditCard": "デビットカードまたはクレジットカード", "cardDescription": "ビザ、マスターカード、アメリカン・エキスプレス", "payWithCrypto": "暗号通貨で支払う", "cryptoDescription": "ビットコイン、イーサリアム、その他の暗号通貨", "profileMenu.guide": "ガイド", "profileMenu.logo": "ロゴ", "profileMenu.settings": "設定", "profileMenu.components": "コンポーネント", "loadingMoreItems": "アイテムをさらに読み込んでいます...", "promptLabel": "プロンプト：", "videoExamples": "ビデオの例", "videoExamplesDescription": "これらのビデオ例とそれらのプロンプトおよび設定を調べてください。「このプロンプトを使用」ボタンをクリックすると、プロンプトを入力欄にコピーできます。", "useThisPrompt": "このプロンプトを使用してください。", "model": "モデル", "duration": "期間", "videoTypeSelection": "ビデオタイプを選択", "notifications.title": "Notifications", "notifications.description": "Your recent notifications and updates", "notifications.totalCount": "{count} notifications", "notifications.markAllRead": "Mark all as read", "notifications.loadMore": "Load more", "notifications.close": "Close", "notifications.empty.title": "No notifications", "notifications.empty.description": "You're all caught up! No new notifications to show.", "notifications.error.title": "Error loading notifications", "notifications.types.default.title": "Notification", "notifications.types.default.description": "You have a new notification", "notifications.types.video_1.title": "ビデオ生成が保留中", "notifications.types.video_1.description": "ビデオ生成は処理待ちです。", "notifications.types.video_2.title": "ビデオ生成が完了しました。", "notifications.types.video_2.description": "ビデオが正常に生成されました。", "notifications.types.video_3.title": "ビデオ生成に失敗しました。", "notifications.types.video_3.description": "ビデオの生成に失敗しました", "notifications.types.image_1.title": "画像生成待ち", "notifications.types.image_1.description": "画像生成は処理待ちです。", "notifications.types.image_2.title": "画像生成完了", "notifications.types.image_2.description": "画像が正常に生成されました。", "notifications.types.image_3.title": "画像の生成に失敗しました。", "notifications.types.image_3.description": "画像生成に失敗しました。", "notifications.types.tts_history_1.title": "オーディオ生成保留中", "notifications.types.tts_history_1.description": "音声合成は処理待ちです。", "notifications.types.tts_history_2.title": "オーディオ生成が完了しました。", "notifications.types.tts_history_2.description": "テキスト読み上げ音声が正常に生成されました。", "notifications.types.tts_history_3.title": "オーディオの生成に失敗しました。", "notifications.types.tts_history_3.description": "音声合成に失敗しました。", "notifications.types.voice_training_1.title": "発声トレーニング保留中", "notifications.types.voice_training_1.description": "ボイストレーニングは処理待ちです。", "notifications.types.voice_training_2.title": "ボイストレーニング完了", "notifications.types.voice_training_2.description": "カスタム音声モデルのトレーニングが無事に完了しました。", "notifications.types.voice_training_3.title": "ボイストレーニングが失敗しました。", "notifications.types.voice_training_3.description": "ボイストレーニングに失敗しました。", "notifications.types.music_1.title": "音楽生成保留", "notifications.types.music_1.description": "音楽生成が処理を待っています。", "notifications.types.music_2.title": "音楽生成完了", "notifications.types.music_2.description": "AI音楽が正常に生成されました。", "notifications.types.music_3.title": "音楽の生成に失敗しました。", "notifications.types.music_3.description": "音楽の生成に失敗しました。", "notifications.types.speech_1.title": "スピーチ生成保留中", "notifications.types.speech_1.description": "あなたのスピーチ生成リクエストは処理待ちです。", "notifications.types.speech_2.title": "音声生成完了", "notifications.types.speech_2.description": "スピーチの生成が成功しました。", "notifications.types.speech_3.title": "スピーチ生成に失敗しました。", "notifications.types.speech_3.description": "音声生成に失敗しました。もう一度お試しください。", "notifications.time.justNow": "たった今", "notifications.time.minutesAgo": "{minutes}分前", "notifications.time.hoursAgo": "{hours}時間前", "notifications.time.yesterday": "昨日", "notifications.status.processing.title": "処理", "notifications.status.processing.description": "ご依頼は処理中です。", "notifications.status.success.title": "完了", "notifications.status.success.description": "正常に完了しました。", "notifications.status.failed.title": "失敗しました", "notifications.status.failed.description": "処理中にエラーが発生しました。", "notifications.status.warning.title": "警告", "notifications.status.warning.description": "警告付きで完了しました。", "notifications.status.pending.title": "保留中", "notifications.status.pending.description": "処理待ち", "notifications.status.cancelled.title": "キャンセルされた", "notifications.status.cancelled.description": "リクエストはキャンセルされました。", "footer.nuxtUIOnDiscord": "Nuxt UI on Discord", "profileSettings.emailNotifications": "Email Notifications", "profileSettings.marketingEmails": "Marketing Emails", "profileSettings.securityAlerts": "Security Alerts", "settings": "設定", "userMenu.profile": "プロフィール", "userMenu.buyCredits": "クレジット購入", "userMenu.settings": "設定", "userMenu.api": "API", "userMenu.logout": "ログアウト", "userMenu.greeting": "こんにちは、{name}", "formats.mp3": "MP3", "formats.wav": "WAV", "channels.mono": "モノラル", "channels.stereo": "ステレオ", "options.allow": "許可", "options.dontAllow": "許可しない", "options.voices": "音声", "options.pickVoice": "音声を選択", "voiceTypes.systemVoices": "システム音声", "voiceTypes.customVoices": "カスタム音声", "voiceTypes.premiumVoices": "プレミアム音声", "voiceTypes.userVoices": "ユーザー音声", "common.home": "ホーム", "Describe the speech you want to generate...": "生成したいスピーチを説明してください...", "listenToSpeech": "スピーチを聞く", "generateSimilar": "類似を生成する", "voice": "声", "emotion": "感情", "speed": "スピード", "speed_settings": "速度設定", "speed_value": "速度値", "speed_slider": "スピードスライダー", "apply": "適用する", "speech_settings": "スピーチ設定", "current_speed": "現在の速度", "reset_defaults": "デフォルトにリセット", "outputFormat": "出力形式", "outputChannel": "出力チャンネル", "selectVoice": "音声を選択", "selectEmotion": "感情を選択", "selectFormat": "形式を選択", "selectChannel": "チャネルを選択", "noVoicesAvailable": "利用可能な音声がありません。", "noEmotionsAvailable": "感情が利用できません。", "searchVoices": "音声を検索...", "searchEmotions": "感情を探す...", "noVoicesFound": "声が見つかりませんでした。", "noEmotionsFound": "感情が見つかりませんでした。", "retry": "再試行", "noAudioSample": "音声サンプルは利用できません。", "Speech Generation Complete": "音声生成完了", "Your speech has been generated successfully": "あなたのスピーチは正常に生成されました。", "stripe": "ストライプ", "stripeDescription": "Stripeで安全に支払う", "history.tabs.imagen": "イマヘン", "history.tabs.video": "ビデオ", "history.tabs.speech": "スピーチ", "history.tabs.music": "音楽", "history.tabs.history": "歴史", "orders.title": "注文履歴", "orders.description": "取引と支払い履歴を表示", "orders.orderId": "注文ID", "orders.amount": "量", "orders.credits": "クレジット", "orders.quantity": "数量", "orders.platform": "プラットフォーム", "orders.externalId": "取引ID", "orders.status.completed": "完了しました", "orders.status.success": "成功", "orders.status.paid": "支払済み", "orders.status.pending": "保留中", "orders.status.processing": "処理中", "orders.status.failed": "失敗しました", "orders.status.cancelled": "キャンセルされました。", "orders.status.error": "エラー", "orders.empty.title": "まだ注文がありません。", "orders.empty.description": "まだ注文をしていません。クレジットを購入して、当社のサービスを利用し始めましょう。", "orders.empty.action": "クレジットを購入", "orders.endOfList": "すべての注文を見ました。", "orders.errors.fetchFailed": "注文履歴の読み込みに失敗しました。もう一度お試しください。", "orders.meta.title": "注文履歴 - Imagen AI", "orders.meta.description": "あなたの取引と支払い履歴をImagen AIで確認する", "historyPages.imagenDescription": "AI生成の画像やアートワークを閲覧する", "historyPages.musicDescription": "AI生成の音楽や音声コンテンツを閲覧する", "historyPages.speechDescription": "AI生成のスピーチと音声コンテンツを閲覧する", "historyPages.videoDescription": "AI生成のビデオやアニメーションを閲覧する", "historyPages.imagenBreadcrumb": "画像", "historyPages.musicBreadcrumb": "音楽", "historyPages.speechBreadcrumb": "スピーチ", "historyPages.videoBreadcrumb": "ビデオ生成", "historyPages.endOfImagesHistory": "画像履歴の終わりに到達しました。", "historyPages.endOfMusicHistory": "あなたは音楽の歴史の終わりにたどり着きました。", "historyPages.endOfSpeechHistory": "あなたはスピーチの履歴の終わりに達しました。", "historyPages.endOfVideoHistory": "ビデオ履歴の終わりに到達しました。", "historyPages.noVideosFound": "ビデオが見つかりませんでした。", "historyPages.noVideosFoundDescription": "ビデオを生成し始めて、ここで見ることができます。", "historyPages.backToLibrary": "図書館に戻る", "historyPages.errorLoadingVideo": "ビデオの読み込みエラー", "historyPages.loadingVideoDetails": "ビデオの詳細を読み込んでいます...", "historyPages.videoDetails": "ビデオの詳細", "historyPages.videoInformation": "ビデオ情報", "historyPages.videoNotFound": "お探しのビデオが見つからないか、読み込めませんでした。", "historyPages.aiContentLibraryTitle": "AIコンテンツライブラリ", "historyPages.aiContentLibraryDescription": "AI生成コンテンツをさまざまなカテゴリで閲覧し、管理する", "demo.notifications.title": "通知の種類とステータスのデモ", "demo.notifications.description": "さまざまな通知タイプと異なるステータス状態の例", "demo.notifications.statusLegend": "ステータス伝説", "demo.speechVoiceSelect.title": "スピーチボイスセレクトデモ", "demo.speechVoiceSelect.description": "モデル値プロップを使用して再利用可能なBaseSpeechVoiceSelectModalコンポーネントを示す", "aspectRatio": "アスペクト比", "Image Reference": "画像参照", "personGeneration.dontAllow": "Don't Allow", "personGeneration.allowAdult": "Allow Adult", "personGeneration.allowAll": "Allow All", "safety_filter_level": "安全フィルターレベル", "used_credit": "使用済みクレジット", "Safety Filter": "安全フィルター", "safetyFilter.blockLowAndAbove": "ブロック・ロー・アンド・アボーブ", "safetyFilter.blockMediumAndAbove": "中型以上のブロック", "safetyFilter.blockOnlyHigh": "ブロックのみ高い", "safetyFilter.blockNone": "ブロックなし", "historyFilter.all": "すべて", "historyFilter.imagen": "画像", "historyFilter.videoGen": "ビデオジェン", "historyFilter.speechGen": "スピーチジェン", "Person Generation": "人物生成", "downloadImage": "画像をダウンロード", "noImageAvailable": "画像はありません。", "enhancePrompt": "プロンプトを強化する", "addImages": "画像を追加", "generateVideo": "ビデオを生成する", "happy": "ハッピー", "sad": "悲しい", "angry": "怒っている", "excited": "エキサイト", "laughing": "笑う", "crying": "泣く", "calm": "落ち着き", "serious": "シリアス", "frustrated": "イライラした", "hopeful": "希望に満ちた", "narrative": "ナラティブ", "kids' storytelling": "子どものストーリーテリング", "audiobook": "オーディオブック", "poetic": "詩的な", "mysterious": "神秘的な", "inspirational": "インスピレーショナル", "surprised": "驚いた", "confident": "自信がある", "romantic": "ロマンチック", "scared": "怖い", "trailer voice": "予告編の声", "advertising": "広告", "documentary": "ドキュメンタリー", "newsreader": "ニュースリーダー", "weather report": "天気予報", "game commentary": "ゲーム解説", "interactive": "インタラクティブ", "customer support": "カスタマーサポート", "playful": "遊び心のある", "tired": "疲れた", "sarcastic": "皮肉な", "disgusted": "嫌悪感", "whispering": "ささやき", "persuasive": "説得力のある", "nostalgic": "ノスタルジック", "meditative": "瞑想的な", "announcement": "お知らせ", "professional pitch": "プロフェッショナルピッチ", "casual": "カジュアル", "exciting trailer": "エキサイティングな予告編", "dramatic": "ドラマチック", "corporate": "法人", "tech enthusiast": "技術愛好家", "youthful": "若々しい", "calming reassurance": "落ち着く安心感", "heroic": "英雄的", "festive": "祝祭の", "urgent": "緊急", "motivational": "モチベーショナル", "friendly": "フレンドリー", "energetic": "エネルギッシュ", "serene": "穏やか", "bold": "太字", "charming": "魅力的な", "monotone": "モノトーン", "questioning": "質問する", "directive": "指令", "dreamy": "夢のような", "epic": "エピック", "lyrical": "リリ<PERSON><PERSON>", "mystical": "神秘的", "melancholy": "憂鬱", "cheerful": "陽気な", "eerie": "不気味な", "flirtatious": "浮気っぽい", "thoughtful": "思慮深い", "cinematic": "シネマティック", "humorous": "ユーモラス", "instructional": "指導の", "conversational": "会話的な", "apologetic": "謝罪の意を表す", "excuse-making": "言い訳作り", "encouraging": "励まし", "neutral": "中立", "authoritative": "権威ある", "sarcastic cheerful": "皮肉な陽気さ", "reassuring": "安心させる", "formal": "フォーマル", "anguished": "苦しんでいる", "giggling": "くすくす笑う", "exaggerated": "誇張された", "cold": "寒い", "hot-tempered": "短気な", "grateful": "感謝している", "regretful": "後悔している", "provocative": "挑発的な", "triumphant": "勝利の", "vengeful": "復讐心に燃える", "heroic narration": "英雄的な語り", "villainous": "ヴィランズ", "hypnotic": "催眠的", "desperate": "必死の", "lamenting": "嘆く", "celebratory": "祝賀の", "teasing": "からかい", "exhausted": "疲れた", "questioning suspicious": "疑念を持つ", "optimistic": "楽観的", "bright, gentle voice, expressing excitement.": "明るく穏やかな声で、興奮を表現する。", "low, slow voice, conveying deep emotions.": "低くゆっくりとした声で、深い感情を伝える。", "sharp, exaggerated voice, expressing frustration.": "鋭く誇張された声が不満を表している。", "fast, lively voice, full of enthusiasm.": "速く、活気に満ちた声、熱意にあふれている。", "interrupted, joyful voice, interspersed with laughter.": "途切れ途切れの喜びに満ちた声、笑い声が混じっている。", "shaky, low voice, expressing pain.": "震えて低い声で、痛みを表現している。", "gentle, steady voice, providing reassurance.": "優しく、安定した声で安心感を与える。", "mature, clear voice, suitable for formal content.": "成熟した明瞭な声で、フォーマルな内容に適しています。", "weary, slightly irritated voice.": "疲れた、少しいら立った声。", "bright voice, conveying positivity and hope.": "明るい声、ポジティブさと希望を伝える。", "natural, gentle voice with a slow rhythm.": "自然で穏やかな声で、ゆっくりとしたリズム。", "lively, engaging voice, captivating for children.": "活気があり、魅力的な声で、子供たちを惹きつける。", "even, slow voice, emphasizing content meaning.": "ゆっくりした声で、内容の意味を強調する。", "rhythmic, emotional voice, conveying subtlety.": "リズミカルで感情的な声が微妙さを伝える。", "low, slow voice, evoking curiosity.": "低くてゆっくりとした声が好奇心をそそる。", "strong, passionate voice, driving action.": "強く情熱的な声が行動を促す。", "high, interrupted voice, expressing astonishment.": "高く途切れた声で驚きを表現する。", "firm, powerful voice, persuasive and assuring.": "力強く説得力のある、安心させる声。", "sweet, gentle voice, suitable for emotional content.": "甘く優しい声、感情的な内容に適しています。", "shaky, interrupted voice, conveying anxiety.": "震えた中断された声で不安を伝えている。", "deep, strong voice with emphasis, creating suspense.": "深く強い声で強調をつけて、緊張感を生み出す。", "engaging, lively voice, emphasizing product benefits.": "魅力的で生き生きとした声で、製品の利点を強調します。", "formal, clear voice with focus on key points.": "正式で明確な声で、重要な点に焦点を当てる。", "calm, profound voice, delivering authenticity.": "穏やかで深みのある声で、本物らしさを伝える。", "standard, neutral voice, clear and precise.": "標準的で中立的な声、明瞭で正確。", "bright, neutral voice, suitable for concise updates.": "明るく中立的な声で、簡潔な更新に適しています。", "fast, lively voice, stimulating excitement.": "速くて活気ある声が興奮を刺激する。", "friendly, approachable voice, encouraging engagement.": "親しみやすく、接しやすい声で、積極的な交流を促す。", "empathetic, gentle voice, easy to connect with.": "共感的で優しい声、親しみやすい。", "clear voice, emphasizing questions and answers.": "声をはっきりさせ、質問と答えを強調する。", "cheerful, playful voice with a hint of mischief.": "陽気で遊び心のある声に少しのいたずらっぽさ。", "slow, soft voice lacking energy.": "遅く、活力のない穏やかな声。", "ironic, sharp voice, sometimes humorous.": "アイロニックで鋭い声、時にはユーモラス。", "cold voice, clearly expressing discomfort.": "冷たい声、明らかに不快感を表している。", "soft, mysterious voice, creating intimacy.": "柔らかく神秘的な声が親密さを生み出します。", "emotional voice, convincing the listener to act.": "感情的な声で、聞き手を行動に駆り立てる。", "gentle voice, evoking feelings of reminiscence.": "穏やかな声は、懐かしい思いを呼び起こす。", "even, relaxing voice, suitable for mindfulness.": "穏やかでリラックスできる声、マインドフルネスに適しています。", "clear voice, emphasizing key words.": "はっきりとした声で、キーワードを強調する。", "confident, clear voice, ideal for business presentations.": "自信に満ちた明瞭な声、ビジネスプレゼンテーションに最適です。", "natural, friendly voice, as if talking to a friend.": "自然で親しみやすい声で、まるで友達と話しているかのように。", "fast, powerful voice, creating tension and excitement.": "速くて力強い声が緊張感と興奮を生み出す。", "emphasized, suspenseful voice, creating intensity.": "強調された、緊張感のある声で、強度を生み出す。", "professional, formal voice, suitable for business content.": "プロフェッショナルでフォーマルな声、ビジネスコンテンツに適しています。", "energetic, lively voice, introducing new technologies.": "エネルギッシュで活気のある声、新技術を紹介します。", "vibrant, cheerful voice, appealing to younger audiences.": "活気に満ちた陽気な声で、若い世代に訴えかける。", "gentle, empathetic voice, easing concerns.": "優しく、共感的な声が不安を和らげます。", "strong, decisive voice, full of inspiration.": "力強く、決断力のある声、インスピレーションに満ちています。", "bright, excited voice, suitable for celebrations.": "明るく、興奮した声、祝祭に適しています。", "fast, strong voice, emphasizing urgency.": "迅速で力強い声、緊急性を強調する。", "passionate, inspiring voice, encouraging action.": "情熱的で感動的な声が行動を促す。", "warm, approachable voice, fostering connection.": "温かく親しみやすい声で、つながりを育む。", "fast, powerful voice, brimming with enthusiasm.": "速くて力強い声が、熱意に満ち溢れている。", "slow, gentle voice, evoking peace and tranquility.": "ゆっくりとした穏やかな声が、平和と安らぎを呼び起こします。", "firm, assertive voice, exuding confidence.": "しっかりとした毅然とした声、自信に満ちている。", "warm, captivating voice, leaving a strong impression.": "温かく魅力的な声が強い印象を残す。", "flat, unvaried voice, conveying neutrality or irony.": "平坦で変化のない声は、中立性や皮肉を伝える。", "curious voice, emphasizing questions.": "好奇心に満ちた声で、質問を強調している。", "firm, clear voice, guiding the listener step-by-step.": "しっかりとした明確な声で、聞き手を一歩一歩導く。", "gentle, slow voice, evoking a floating sensation.": "優しいゆっくりとした声が、浮遊感を呼び起こす。", "deep, resonant voice, emphasizing grandeur.": "深く、響き渡る声が壮大さを強調する。", "soft, melodic voice, similar to singing.": "柔らかくメロディックな声で、歌うことに似ています。", "low, drawn-out voice, evoking mystery.": "低く引き伸ばした声が神秘を感じさせる。", "slow, low voice, conveying deep sadness.": "ゆっくりとした低い声が、深い悲しみを伝えている。", "bright, energetic voice, full of positivity.": "明るく活気に満ちた声、ポジティブさにあふれている。", "low, whispery voice, evoking fear or strangeness.": "低くて囁くような声で、恐怖や不気味さを呼び起こす。", "sweet, teasing voice, full of allure.": "甘美で魅惑に満ちたからかうような声。", "slow, reflective voice, full of contemplation.": "ゆっくりとした、内省的な声、熟考に満ちている。", "resonant, emphasized voice, creating a movie-like effect.": "響き渡る強調された声が映画のような効果を生み出す。", "lighthearted, cheerful voice, sometimes exaggerated.": "陽気で明るい声、時に誇張されることもある。", "clear, slow voice, guiding the listener step-by-step.": "はっきりとしたゆっくりとした声で、聞き手を一歩一歩案内します。", "natural voice, as if chatting with the listener.": "自然な声で、リスナーと会話しているかのように。", "soft, sincere voice, expressing regret.": "柔らかで誠実な声で、後悔を表している。", "hesitant, uncertain voice, sometimes awkward.": "ためらいがちで不確かな声、ときにはぎこちない。", "warm voice, providing motivation and support.": "温かい声で、やる気とサポートを提供します。", "even voice, free of emotional bias.": "感情的な偏りのない均一な声。", "strong, powerful voice, exuding credibility.": "力強く、説得力に満ちた声。", "cheerful voice with an undertone of mockery.": "陽気な声に嘲弄の響きがある。", "gentle, empathetic voice, providing comfort.": "優しく、共感的な声で安心感を与える。", "clear, polite voice, suited for formal occasions.": "明確で丁寧な声は、フォーマルな場面に適しています。", "urgent, shaky voice, expressing distress.": "緊急で、不安定な声で苦悩を表現している。", "interrupted voice, mixed with light laughter.": "途切れた声が軽い笑いと混じる。", "loud, emphasized voice, often humorous.": "大声で強調された声、ユーモラスなことが多い。", "flat, unemotional voice, conveying detachment.": "平坦で感情のない声、無関心を伝える。", "fast, sharp voice, sometimes out of control.": "速くて鋭い声で、時々制御不能になる。", "warm, sincere voice, expressing appreciation.": "暖かく、誠実な声で感謝の意を表す。", "low, subdued voice, full of remorse.": "低く抑えた声、後悔に満ちている。", "challenging, strong voice, full of insinuation.": "挑戦的で、力強い声、含意に満ちている。", "loud, powerful voice, full of victory.": "大きく力強い声、勝利に満ちている。", "low, cold voice, expressing determination for revenge.": "低く冷たい声、復讐への決意を表す。", "strong, inspiring voice, emphasizing heroic deeds.": "力強く、感動を呼び起こす声、英雄的な行いを強調する。", "low, drawn-out voice, full of scheming.": "低く、引き伸ばされた声で、策略に満ちている。", "even, repetitive voice, drawing the listener in.": "均一で反復的な声が、聞き手を引き込む。", "urgent, shaky voice, expressing hopelessness.": "緊急で不安定な声、絶望感を表している。", "low, sorrowful voice, as if mourning.": "低く、悲しげな声で、まるで悲しんでいるかのように。", "excited, joyful voice, full of festive spirit.": "興奮して喜びに満ちた声、祝祭の精神であふれています。", "light, playful voice, sometimes mockingly.": "軽やかで遊び心のある声、ときにからかうように。", "weak, broken voice, expressing extreme fatigue.": "弱々しく、疲労困憊した声。", "slow, emphasized voice, full of suspicion.": "遅く、強調された声、疑念に満ちた声。", "bright, hopeful voice, creating positivity.": "明るく希望に満ちた声で、ポジティブさを生み出す。", "Your audio will be processed with the latest stable model.": "あなたの音声は最新の安定したモデルで処理されます。", "Your audio will be processed with the latest beta model.": "あなたの音声は最新のベータモデルで処理されます。", "You don't have any saved prompts yet.": "まだ保存されたプロンプトがありません。", "Commercial Use": "商業利用", "Other people’s privacy": "他人のプライバシー", "You must respect the privacy of others when using our services. Do not upload or create speech output containing personal information, confidential data, or copyrighted material without permission.": "当社のサービスを利用する際には、他人のプライバシーを尊重しなければなりません。許可なしに個人情報、機密データ、または著作権で保護された資料を含む音声出力をアップロードしたり作成したりしないでください。", "{price}$ per credit": "クレジットあたり{price}ドル", "Pricing": "価格設定", "Simple and flexible. Only pay for what you use.": "シンプルで柔軟。使った分だけ支払う。", "Pay as you go": "使った分だけ支払う", "Flexible": "柔軟な", "Input characters": "入力文字", "Audio model": "オーディオモデル", "Credits": "クレジット", "Cost": "費用", "HD quality voices": "HD品質の音声", "Advanced model": "高度なモデル", "Buy now": "今すぐ購入", "Paste your text to calculate": "テキストを貼り付けて計算してください", "Paste your text here...": "こちらにテキストを貼り付けてください...", "Calculate": "計算する", "Estimate your cost by drag the slider below or": "スライダーをドラッグしてコストを見積もるか", "calming": "落ち着く", "customer": "顧客", "exciting": "エキサイティング", "excuse": "すみません", "game": "ゲーム", "hot": "ホット", "kids": "子供たち", "professional": "プロフェッショナル", "tech": "テクノロジー", "trailer": "予告編", "weather": "天気", "No thumbnail available": "サムネイルは利用できません。", "Debit or Credit Card": "デビットカードまたはクレジットカード", "Visa, Mastercard, American Express and more": "ビザ、マスターカード、アメリカン・エキスプレスなど", "Top up now": "今すぐチャージ", "noVideoAvailable": "ビデオは利用できません。", "common.back": "戻る", "common.edit": "編集", "common.save": "保存", "common.cancel": "キャンセル", "common.delete": "削除", "common.copy": "コピー", "common.copied": "コピーしました。", "common.manage": "管理する", "aiToolMenu.textToImage": "テキストから画像へ", "profileMenu.integration": "統合", "videoTypes.examples.tikTokDanceTrend": "TikTokダンストレンド", "videoTypes.examples.energeticDanceDescription": "エネルギッシュなダンスビデオ、鮮やかな色彩、素早いカット、トレンディな音楽、縦型フォーマット、ソーシャルメディアスタイル。", "videoTypes.examples.instagramReel": "インスタグラムリール", "videoTypes.examples.lifestyleDescription": "ライフスタイルコンテンツには、美しいビジュアル、スムーズなトランジション、トレンディなエフェクト、魅力的なストーリーテリングが含まれています。", "videoTypes.examples.comedySketch": "コメディスケッチ", "videoTypes.examples.funnyComedyDescription": "面白いコメディシーン、表情豊かなキャラクター、ユーモラスな状況、楽しませる会話、軽快なムード。", "videoTypes.examples.productLaunchAd": "製品発売広告", "videoTypes.examples.professionalCorporateDescription": "プロフェッショナルな企業用ビデオ、経営層のプレゼンテーション、清潔なオフィス環境、ビジネスフォーマルなスタイル", "videoTypes.examples.quickPromoVideo": "クイックプロモ動画", "videoTypes.examples.fastPacedPromoDescription": "迅速なプロモーションコンテンツ、効率的な制作、コスト効率の良いビジュアル、簡潔なメッセージング", "videoTypes.examples.birthdayGreeting": "誕生日のお祝い", "videoTypes.examples.personalizedBirthdayDescription": "個別の誕生日ビデオは、祝祭の装飾、暖かな照明、祝いの雰囲気、心のこもったメッセージが特徴です。", "videoTypes.examples.brandStoryVideo": "ブランドストーリービデオ", "videoTypes.examples.tutorialVideo": "チュートリアルビデオ", "videoTypes.examples.manOnThePhone": "電話中の男性", "videoTypes.examples.runningSnowLeopard": "雪豹のランニング", "videoTypes.examples.snowLeopard": "ユキヒョウ", "videoTypes.styles.cartoonAnimated": "アニメーションスタイルのビデオ", "videoTypes.styles.naturalDocumentary": "自然なドキュメンタリー風の映像", "videoTypes.styles.naturalLifelike": "自然でリアルなビデオスタイル", "videoTypes.styles.professionalMovieQuality": "劇的な照明によるプロの映画のような品質", "videoTypes.styles.creativeStylized": "クリエイティブでスタイリッシュなビデオエフェクト", "videoTypes.styles.retroVintage": "レトロまたはヴィンテージのビデオ美学", "historyFilter.dialogueGen": "ダイアログジェン", "historyFilter.speechGenDocument": "ドキュメントからのスピーチ生成", "demo.notifications.availableNotificationTypes": "利用可能な通知タイプ", "demo.speechVoiceSelect.example1": "例1：デフォルトの使用法", "demo.speechVoiceSelect.example2": "例2: 小サイズ", "demo.speechVoiceSelect.example3": "例3: 大型サイズ", "demo.speechVoiceSelect.example4": "例4：複数のエラー例。", "demo.speechVoiceSelect.example5": "例5：ステータス比較", "demo.speechVoiceSelect.mainNarrator": "メインナレーター", "demo.speechVoiceSelect.characterVoice": "キャラクターボイス", "demo.speechVoiceSelect.selectedVoicesSummary": "選ばれた声の要約:", "demo.speechVoiceSelect.clearAll": "すべてクリア", "demo.speechVoiceSelect.setRandomVoices": "ランダムな声を設定", "demo.speechVoiceSelect.logToConsole": "コンソールにログを出力", "demo.speechVoiceSelect.notSelected": "選ばれていません", "demo.speechVoiceSelect.voiceSelectionsChanged": "音声の選択が変更されました。", "demo.historyWrapper.title": "ヒストリラッパー ステータス バッジ デモ", "demo.historyWrapper.normalStatus": "例1: 通常状態 (ステータス = 1)", "demo.historyWrapper.processingStatus": "例 2: 処理状況 (ステータス = 2)", "demo.historyWrapper.errorStatus": "例 3: エラーステータス (ステータス = 3) - エラーバッジを表示", "demo.historyWrapper.multipleErrorExamples": "例4：複数のエラー例", "demo.historyWrapper.statusComparison": "例5: ステータスの比較", "demo.historyWrapper.normalImageGeneration": "通常画像生成", "demo.historyWrapper.videoGenerationInProgress": "ビデオ生成中", "demo.historyWrapper.speechGenerationFailed": "音声生成に失敗しました。", "demo.historyWrapper.imageFailed": "画像の読み込みに失敗しました", "demo.historyWrapper.videoFailed": "ビデオが失敗しました。", "demo.historyWrapper.speechFailed": "スピーチに失敗しました。", "demo.historyWrapper.statusSuccess": "ステータス: 成功", "demo.historyWrapper.statusProcessing": "ステータス：処理中", "demo.historyWrapper.statusError": "ステータス：エラー", "demo.historyWrapper.status1Success": "ステータス1：成功", "demo.historyWrapper.status2Processing": "ステータス2: 処理中", "demo.historyWrapper.badgeBehavior": "バッジの行動:", "demo.historyWrapper.showsOnlyTypeAndStyle": "タイプとスタイルのバッジのみを表示", "demo.historyWrapper.showsTypeStyleAndError": "タイプ、スタイル、警告アイコン付きの赤いエラーバッジを表示", "demo.historyWrapper.redBackgroundWithWhite": "赤い背景に白い文字と警告アイコン", "demo.historyWrapper.allBadgesHideOnHover": "すべてのバッジは、オーバーレイコンテンツを表示するためにホバーで隠れます。", "demo.speechVoiceCaching.title": "スピーチボイスキャッシングテスト", "demo.speechVoiceCaching.description": "コンポーネント間での音声キャッシュをテストするためのテスト", "demo.speechVoiceCaching.component1Modal": "コンポーネント1 - モーダル", "demo.speechVoiceCaching.component3RegularSelect": "コンポーネント3 - 通常セレクト", "demo.speechVoiceCaching.forceReloadVoices": "ボイスを強制リロード", "demo.speechVoiceCaching.clearAllSelections": "すべての選択をクリア", "demo.speechVoiceCaching.logStoreState": "ログストア状態", "demo.speechVoiceCaching.refreshPageInstructions": "ページを更新して、任意のコンポーネントを開くと、再読み込みされます。", "demo.speechVoiceCaching.checkNetworkTab": "ネットワークタブを確認してAPIコールを確認する", "demo.speechVoiceCaching.selectedVoicePersist": "選択した音声はlocalStorageを通じて永続化されます。", "demo.speechVoiceCaching.pageMounted": "ページがマウントされると、必要に応じてストアが自動的に初期化されます。", "integration.title": "統合", "integration.subtitle": "APIキーと統合設定を管理する", "integration.apiKeys": "APIキー", "integration.apiKeysDescription": "プログラムによるアクセスのためのAPIキーを管理する", "integration.webhook": "ウェブフック", "integration.webhookDescription": "通知のためのWebhook URLを設定する", "apiKeys.title": "APIキー", "apiKeys.subtitle": "プログラムによるアクセスのためのAPIキーを管理する", "apiKeys.create": "APIキーを作成", "apiKeys.createNew": "新しいAPIキーを作成", "apiKeys.createFirst": "最初のAPIキーを作成", "apiKeys.name": "名前", "apiKeys.nameDescription": "APIキーに説明的な名前を付けてください。", "apiKeys.namePlaceholder": "例: 私のアプリのAPIキー", "apiKeys.nameRequired": "APIキー名が必要です。", "apiKeys.createdAt": "作成された", "apiKeys.noKeys": "APIキーはありません。", "apiKeys.noKeysDescription": "プログラムによるアクセスを開始するために最初のAPIキーを作成してください。", "apiKeys.created": "APIキーが正常に作成されました。", "apiKeys.createError": "APIキーの作成に失敗しました。", "apiKeys.deleted": "APIキーが正常に削除されました。", "apiKeys.deleteError": "APIキーの削除に失敗しました。", "apiKeys.deleteConfirm": "APIキーを削除", "apiKeys.deleteWarning": "このAPIキーを削除してもよろしいですか？この操作は元に戻せません。", "apiKeys.copied": "APIキーがクリップボードにコピーされました", "apiKeys.copyError": "APIキーのコピーに失敗しました。", "webhook.title": "Webhook設定", "webhook.subtitle": "リアルタイム通知用のWebhook URLを設定する", "webhook.configuration": "Webhook URL", "webhook.currentUrl": "現在のWebhook URL", "webhook.currentUrlDescription": "このURLは、WebhookイベントのPOSTリクエストを受信します。", "webhook.notConfigured": "Webhook URLが設定されていません。", "webhook.url": "Webhook URL", "webhook.urlDescription": "Webhook通知を受信したいURLを入力してください。", "webhook.urlPlaceholder": "https://your-domain.com/webhook", "webhook.urlRequired": "まずはウェブフックのURLを入力してください。", "webhook.invalidUrl": "有効なURLを入力してください。", "webhook.saved": "Webhook URLが正常に保存されました。", "webhook.saveError": "Webhook URLの保存に失敗しました。", "webhook.test": "テスト", "webhook.testSent": "テスト送信", "webhook.testDescription": "テストのウェブフックが正常に送信されました。", "webhook.information": "ウェブフック情報", "webhook.howItWorks": "仕組み", "webhook.description": "設定時に、アカウント内で特定のイベントが発生するたびに、HTTP POSTリクエストをあなたのWebhook URLに送信します。", "webhook.events": "ウェブフックイベント", "webhook.imageGenerated": "画像生成が完了しました", "webhook.imageGenerationFailed": "画像の生成に失敗しました。", "webhook.creditUpdated": "クレジット残高が更新されました。", "webhook.payloadFormat": "ペイロードフォーマット", "webhook.payloadDescription": "Webhookリクエストは、次の構造を持つJSONとして送信されます。", "webhook.security": "セキュリティ", "webhook.securityDescription": "HTTPS URLを使用し、署名検証を実装してウェブフックの真正性を保証することをお勧めします。", "error.general": "エラー", "error.validation": "検証エラー", "error.required": "必須項目", "success.saved": "正常に保存されました。", "success.created": "正常に作成されました。", "success.deleted": "削除に成功しました。", "success.copied": "クリップボードにコピーされました。", "confirmDelete": "削除を確認", "confirmDeleteDescription": "この項目を削除してもよろしいですか？この操作は元に戻せません。", "historyDeleted": "履歴アイテムが正常に削除されました。", "deleteError": "履歴項目の削除に失敗しました。", "Regenerate Image": "画像を再生成する", "You haven't made any changes to the settings. Are you sure you want to regenerate the same image?": "設定を変更していません。同じ画像を再生成してもよろしいですか？", "Yes, Regenerate": "はい、再生成します。", "Cancel": "キャンセル", "models.imagen4Fast": "イメージ4ファスト", "models.imagen4Ultra": "イメージ4ウルトラ", "voiceTypes.favoriteVoices": "お気に入りの声", "voiceTypes.geminiVoices": "ジェミニ・ボイシズ", "speech.dialogueGeneration.complete": "対話生成完了", "speech.dialogueGeneration.failed": "対話生成に失敗しました。", "speech.dialogueGeneration.pending": "対話生成保留中", "speech.dialogueGeneration.dialogueGen": "ダイアログ生成", "speech.dialogueGeneration.successMessage": "あなたの対話が正常に生成されました。", "speech.speechGeneration.complete": "音声生成が完了しました。", "speech.speechGeneration.failed": "音声生成に失敗しました。", "speech.speechGeneration.pending": "スピーチ生成保留中", "speech.speechGeneration.successMessage": "あなたのスピーチは正常に生成されました。", "speech.speechGeneration.requestWaiting": "あなたの音声生成リクエストは処理中です。", "speech.errors.failedToLoadEmotions": "感情の読み込みに失敗しました", "tts-document": "ファイルから音声へ", "assignVoicesToSpeakers": "話者に声を割り当てる", "speakers": "スピーカー", "addSpeaker": "スピーカーを追加", "noVoiceAssigned": "音声が割り当てられていません。", "noSpeakersAdded": "スピーカーはまだ追加されていません。", "assignVoiceToSpeaker": "{speaker}に声を割り当てる", "assigned": "割り当て済み", "assign": "割り当てる", "editSpeaker": "スピーカーを編集", "speakerName": "話者名", "enterSpeakerName": "スピーカー名を入力してください", "save": "保存", "speaker": "スピーカー", "assignVoices": "声を割り当てる", "speakersWithVoices": "{assigned}/{total} 人の話者が声を持っています。", "dialogs": "ダイアログ", "addDialog": "ダイアログを追加", "enterDialogText": "ダイアログテキストを入力してください...", "selectSpeaker": "スピーカーを選択", "generateDialogSpeech": "ダイアログスピーチを生成する", "voice 1": "声1", "voice 2": "ボイス2", "uuid": "UUID", "output_format": "出力形式", "output_channel": "出力チャンネル", "file_name": "ファイル名", "file_size": "ファイルサイズ", "speakers_count": "スピーカー数", "custom_prompt": "カスタムプロンプト", "Please wait a moment...": "しばらくお待ちください。", "Click to copy": "クリックしてコピー", "Copied to clipboard": "クリップボードにコピーしました。", "UUID has been copied to clipboard": "UUIDがクリップボードにコピーされました。", "Credits: {credits} remaining": "クレジット: 残り {credits}", "This generation will cost: {cost} Credits": "この世代の費用は: {cost} クレジットです。", "This generation will cost: {cost} Credits for {duration}s": "この生成には{duration}秒で{cost}クレジットかかります", "Your generated video will appear here": "生成されたビデオはここに表示されます。", "Regenerate Video": "ビデオを再生成", "You haven't made any changes to the settings. Are you sure you want to regenerate the same video?": "設定に変更を加えていません。同じビデオを再生成してもよろしいですか？", "Your generated speech will appear here": "生成された音声はここに表示されます。", "Regenerate Speech": "音声を再生成する", "You haven't made any changes to the settings. Are you sure you want to regenerate the same speech?": "設定に変更は加えられていません。同じスピーチを再生成してもよろしいですか？", "Generated Speech": "生成された音声", "Generating speech...": "音声を生成中...", "View Details": "詳細を表示", "Speech Examples": "スピーチの例", "Click on any example to use its prompt for speech generation": "任意の例をクリックして、そのプロンプトを音声生成に使用してください。", "Click to use": "クリックして使用", "videoStyles.selectVideoStyle": "ビデオスタイルを選択", "videoStyles.cinematic": "シネマティック", "videoStyles.realistic": "リアリスティック", "videoStyles.animated": "アニメーション", "videoStyles.artistic": "アーティスティック", "videoStyles.documentary": "ドキュメンタリー", "videoStyles.vintage": "ビンテージ", "ui.buttons.downloadApp": "アプリをダウンロード", "ui.buttons.signUp": "サインアップ", "ui.buttons.viewDetails": "詳細を見る", "ui.buttons.seeLater": "後で見る", "ui.buttons.selectFile": "ファイルを選択", "ui.buttons.selectFiles": "ファイルを選択", "ui.buttons.pickAVoice": "声を選んでください。", "ui.buttons.topUpNow": "今すぐチャージしてください", "ui.buttons.pressEscToClose": "ESCキーを押して終了します。", "ui.labels.clickToCopy": "クリックしてコピー", "ui.labels.copiedToClipboard": "クリップボードにコピーされました。", "ui.labels.noAudioAvailable": "音声は利用できません。", "ui.labels.noThumbnailAvailable": "サムネイルは利用できません。", "ui.labels.noPromptAvailable": "プロンプトは利用できません。", "ui.labels.videoModel": "ビデオモデル", "ui.labels.speechModel": "スピーチモデル", "ui.labels.generatedSpeech": "生成された音声", "ui.labels.defaultVoice": "デフォルトの声", "ui.labels.selectAnyVoice": "任意の声を選択", "ui.labels.cameraMotion": "カメラの動き", "ui.labels.transform": "変換", "ui.labels.transforming": "変換中...", "ui.messages.imageLoaded": "画像が読み込まれました", "ui.messages.imageRevealComplete": "画像の公開が完了しました", "ui.messages.processingImage": "画像を処理中", "ui.messages.videoLoaded": "ビデオが読み込まれました。", "ui.messages.videoProcessing": "ビデオ処理", "ui.messages.invalidDownloadLink": "無効なダウンロードリンク", "ui.messages.pleaseSelectSupportedFile": "サポートされているファイルを選択してください。", "ui.messages.deleteConfirm": "削除を確認", "ui.messages.deleteFailed": "削除に失敗しました。", "ui.messages.youHaveNewNotification": "新しい通知があります。", "ui.messages.yourGenerationIsReady": "あなたの世代は準備ができています。", "ui.errors.errorLoadingImage": "画像の読み込みエラー:", "ui.errors.failedToCopy": "コピーに失敗しました。", "ui.errors.failedToPlayAudioPreview": "オーディオプレビューの再生に失敗しました。", "ui.errors.wavesurferError": "ウェーブサーファーエラー：", "ui.errors.somethingWentWrong": "問題が発生しました。もう一度お試しください。", "ui.errors.supabaseUrlRequired": "SupabaseのURLと匿名キーが必要です。", "dialog.startTypingHere": "ここにダイアログを入力し始めてください...", "payment.debitCreditCard": "デビットまたはクレジットカード", "payment.cardDescription": "ビザ、マスターカード、アメリカン・エキスプレスなど", "Style Description": "スタイル説明", "Dialog Content": "ダイアログの内容", "Your generated dialog will appear here": "あなたの生成されたダイアログはこちらに表示されます。", "Regenerate Dialog": "ダイアログを再生成", "Generated Dialog": "生成されたダイアログ", "Generating dialog...": "ダイアログを生成しています...", "Dialog Information": "ダイアログ情報", "Audio Player": "オーディオプレーヤー", "Voices": "声", "Voice 1": "ボイス1", "Voice 2": "ボイス2", "Dialog Examples": "ダイアログの例", "Click on any example to use its style or dialog content": "任意の例をクリックして、そのスタイルまたはダイアログ内容を使用してください。", "Use Style": "スタイルを使用する", "Use Dialog": "ダイアログを使用", "personGeneration": "人物生成", "Imagen": "画像", "On": "オン", "Off": "オフ", "Prompts will always be refined to improve output quality": "プロンプトは常に出力品質を向上させるために洗練されます。", "Prompts will not be modified": "プロンプトは変更されません。", "Tips": "ヒント", "Your video is still being generated in the background. You can close this page and check the history tab for the generated video and we will notify you when it is ready.": "ビデオはバックグラウンドで生成されています。このページを閉じても、履歴タブで生成されたビデオを確認できます。準備が整いましたらお知らせいたします。", "Go to History": "履歴に移動", "footer.youtube": "ユーチューブ", "footer.doctransGPT": "DoctransGPT", "footer.textToSpeechOpenAI": "テキストを音声に変換するOpenAI", "footer.privacyPolicy": "プライバシーポリシー", "footer.termsOfService": "利用規約", "footer.terms": "条件", "footer.privacy": "プライバシー", "Generate": "生成する", "Prompt": "プロンプト", "Generate Video": "ビデオを生成する", "ui.errors.generationFailed": "生成に失敗しました。", "downloadVideo": "ビデオをダウンロード", "imageStyles.selectImageStyle": "画像スタイルを選択", "imageStyles.none.description": "特定のスタイルは適用されていません。", "imageStyles.3d-render.description": "画像を3Dでレンダリングする", "imageStyles.acrylic.description": "アクリル絵の具のスタイルで画像を作成する", "imageStyles.anime-general.description": "アニメ風の画像を生成する", "imageStyles.creative.description": "創造的な芸術効果を施す", "imageStyles.dynamic.description": "ダイナミックでエネルギッシュなビジュアルを作成する", "imageStyles.fashion.description": "ファッションフォトグラフィーのスタイル画像", "imageStyles.game-concept.description": "ゲームコンセプトアートのデザインイメージ", "imageStyles.graphic-design-3d.description": "3Dグラフィックデザイン要素を適用する", "imageStyles.illustration.description": "イラスト風のアート作品を作成する", "imageStyles.portrait.description": "ポートレート写真用に最適化する", "imageStyles.portrait-cinematic.description": "シネマティックなポートレートスタイルを作成する", "imageStyles.portrait-fashion.description": "ファッションポートレートスタイリングを適用する", "imageStyles.ray-traced.description": "レイトレーシング効果でレンダリングする", "imageStyles.stock-photo.description": "プロフェッショナルなストックフォトスタイルを作成する", "imageStyles.watercolor.description": "水彩画の効果を適用する", "imageStyles.examples": "例", "ui.messages.dragDropOrClick": "ファイルをここにドラッグ＆ドロップするか、クリックして選択してください。", "ui.messages.dropFilesHere": "ファイルをここにドロップしてください", "ui.messages.selectMultipleFiles": "複数のファイルを選択できます。", "ui.messages.selectSingleFile": "ファイルを選択してアップロードしてください。", "ui.messages.supportedFormats": "サポートされている形式", "ui.messages.releaseToUpload": "アップロードするためにリリース", "ui.labels.generatedAudio": "生成された音声", "ui.actions.showResult": "結果を表示", "ui.actions.hideResult": "結果を非表示", "ui.messages.speechGenerating": "音声を生成中...", "Your speech is still being generated in the background. You can close this page and check the history tab for the generated audio and we will notify you when it is ready.": "スピーチはバックグラウンドで生成されています。このページを閉じて、履歴タブで生成された音声を確認できます。準備が整ったらお知らせします。", "downloadAudio": "オーディオをダウンロード", "All Countries": "すべての国", "All Genders": "すべての性別", "Country": "国", "Gender": "ジェンダー", "Reset": "リセット", "Search by name or description...": "名前または説明で検索...", "Male": "男性", "Female": "女性", "American": "アメリカ人", "British": "イギリス人", "Australian": "オーストラリア人", "Indian": "インド人", "Chinese": "中国語", "Spanish": "スペイン語", "Canadian": "カナダ人", "Irish": "アイルランド語", "Singaporean": "シンガポール人", "Russian": "ロシア語", "German": "ドイツ語", "Portuguese": "ポルトガル語", "Hindi": "ヒンディー語", "Mexican": "メキシコの", "Latin American": "ラテンアメリカ人", "Argentine": "アルゼンチン", "Peninsular": "半島", "French": "フランス語", "Parisian": "パリジャン", "Standard": "標準", "Brazilian": "ブラジル人", "Turkish": "トルコ語", "Istanbul": "イスタンブール", "Bavarian": "バイエルン地方", "Polish": "ポーランド語", "Italian": "イタリア語", "South African": "南アフリカの", "Scottish": "スコットランド人", "Welsh": "ウェールズ語", "New Zealand": "ニュージーランド", "Dutch": "オランダ語", "Belgian": "ベルギーの", "Swedish": "スウェーデン語", "Norwegian": "ノルウェー語", "Danish": "デンマーク語", "Korean": "韓国語", "Korean, Seoul": "韓国、ソウル", "Japanese": "日本語", "Croatian": "クロアチア語", "Czech": "チェコ", "Moravian": "モラヴィア人", "Zealandic": "シーランド語", "Indonesian": "インドネシア語", "Javanese": "ジャワ語", "Romanian": "ルーマニア語", "Swiss": "スイス", "Vietnamese": "ベトナム語", "Arabic": "アラビア語", "Bulgarian": "ブルガリア語", "Finnish": "フィンランド語", "Greek": "ギリシャ語", "Hungarian": "ハンガリー語", "Filipino": "フィリピン人", "History": "歴史", "imagen-flash": "ジェミニ2.0フラッシュ", "Detail": "詳細", "Delete": "削除", "ui.errors.unknownError": "不明なエラーが発生しました。", "ui.errors.tryAgainLater": "後でもう一度お試しください。", "More": "もっと", "tts-text": "オーディオ", "tts-multi-speaker": "オーディオ", "tts-history": "オーディオ", "tts-history_1": "オーディオ", "tts-history_2": "オーディオ", "tts-history_3": "オーディオ", "voice-training": "ボイストレーニング", "voice-training_1": "ボイストレーニング", "voice-training_2": "ボイストレーニング", "Start writing or paste your text here or select a file to generate speech...": "テキストをここに書き始めるか、テキストを貼り付けるか、ファイルを選択して音声を生成してください。", "Selecting a voice...": "声を選択中...", "Voices Library": "ヴォイシーズ・ライブラリー", "Select a voice for your speaker from the library.": "ライブラリからスピーカーのための声を選んでください。", "Next": "次へ", "Back": "戻る", "Done": "完了", "I got it!": "わかった！", "historyPages.endOfHistory": "履歴の終わりに到達しました。", "Press ESC to close": "ESCキーを押して閉じる", "Your generated image will appear here": "生成された画像がここに表示されます。", "Generate Speech": "音声を生成する", "Start writing or paste your text here to generate speech...": "ここにテキストを入力または貼り付けて、スピーチを生成してください。", "Video Gen": "ビデオジェン", "Generate videos from text prompts and images.": "テキストプロンプトや画像から動画を生成する。", "Speech Gen": "スピーチジェン", "Convert text and documents to natural speech.": "テキストやドキュメントを自然な音声に変換します。", "Dialogue Gen": "ダイアログ・ジェン", "Create natural conversations with multiple speakers.": "複数の話者との自然な会話を作成します。", "Veo 2": "Veo 2", "Text to Video": "テキストからビデオへ", "Image to Video": "画像から動画への変換", "Up to 8 seconds": "最大8秒", "1080p Quality": "1080p画質", "Multiple Styles": "複数のスタイル", "Text to Speech": "テキスト読み上げ", "Document to Speech": "ドキュメントを音声に変換", "Multi-Speaker Support": "マルチスピーカー対応", "50+ Voices": "50以上の声", "Multiple Languages": "複数の言語", "Emotion Control": "感情制御", "Multi-Speaker Dialogue": "マルチスピーカーダイアログ", "Natural Conversations": "自然な会話", "Voice Customization": "音声カスタマイズ", "Emotion Expression": "感情表現", "Script Generation": "スクリプト生成", "Audio Export": "オーディオエクスポート", "Home": "ホーム", "Price per 1 character: {cost} Credits": "1文字あたりの価格：{cost}クレジット", "veo-2": "Veo 2", "veo-3": "3を見る", "Your speech generation is still being generated in the background. You can close this page and check the history tab for the generated speech and we will notify you when it is ready.": "音声の生成はまだバックグラウンドで行われています。このページを閉じても、履歴タブで生成された音声を確認でき、準備が整った時にはお知らせいたします。", "Create Another": "さらに作成", "estimated_credit": "推定クレジット", "tts-flash": "ジェミニ2.5フラッシュ", "Select Another Voice": "別の声を選択", "Custom prompt {count}": "カスタムプロンプト {count}", "Custom prompt": "カスタムプロンプト", "Prompt name": "プロンプト名", "This name will help you identify your prompt.": "この名前はプロンプトを識別するのに役立ちます。", "Save as new": "新規保存", "Ok, save it!": "わかった、保存して！", "Don't use": "使わないでください", "Use": "使用", "You have the right to use the speech output generated by our services for personal, educational, or commercial purposes.": "私たちのサービスによって生成された音声出力を個人用、教育用、または商業用目的で使用する権利があります。", "Your saved prompts": "保存されたプロンプト", "Success": "成功", "Saved prompt successfully": "プロンプトが正常に保存されました。", "Error": "エラー", "Saved prompt failed": "保存されたプロンプトが失敗しました。", "Updated prompt successfully": "プロンプトが正常に更新されました。", "Updated prompt failed": "更新されたプロンプトが失敗しました。", "Are you sure you want to delete this prompt?": "このプロンプトを削除してもよろしいですか？", "Deleted prompt successfully": "プロンプトを正常に削除しました。", "Deleted prompt failed": "削除されたプロンプトに失敗しました。", "Enter your custom prompt here.": "ここにカスタムプロンプトを入力してください。", "Ex: Funny prompt": "例：面白いプロンプト", "Discard": "破棄する", "Update": "更新", "Edit": "編集", "Custom Prompt": "カスタムプロンプト", "Your credits will never expire.": "あなたのクレジットは決して期限切れにはなりません。", "Available credits": "利用可能なクレジット", "{n}+ Styles": "{n}+ スタイル", "Create images from text prompts.": "テキストプロンプトから画像を作成する。", "/Image": "/イメージ", "/Video": "/ビデオ", "/1 character": "/1 文字", "Buy credits": "クレジットを購入", "My Account": "マイアカウント", "Manage your account, credits, and orders.": "アカウント、クレジット、注文を管理します。", "Full Name": "フルネーム", "Total Available Credits": "利用可能なクレジット総額", "Locked Credits": "ロックされたクレジット", "Save changes": "変更を保存", "Your account has been updated.": "お客様のアカウントが更新されました。", "This field is required.": "このフィールドは必須です。", "User Info": "ユーザー情報", "Email": "メール", "Used to sign in, for email receipts and product updates.": "サインイン、メールの領収書および製品の更新情報に使用されます。", "Active and valid credits only": "有効なクレジットのみ", "We lock your credits to perform transactions.": "取引を行うために、あなたのクレジットをロックします。", "Referral Link": "紹介リンク", "Share your referral link to earn credits.": "紹介リンクを共有してクレジットを獲得しましょう。", "Referral Code": "紹介コード", "Your Referral Code": "あなたの紹介コード", "Copy": "コピー", "Copied!": "コピーしました！", "Orders": "注文", "Manage your orders.": "注文を管理する。", "Will appear on receipts, invoices, and other communication.": "領収書、請求書、その他のコミュニケーションに表示されます。", "User Information": "ユーザー情報", "Must be at least 8 characters": "少なくとも8文字以上である必要があります。", "Passwords must be different": "パスワードは異なる必要があります。", "Passwords must match": "パスワードが一致している必要があります。", "Current password": "現在のパスワード", "New password": "新しいパスワード", "Confirm new password": "新しいパスワードを確認", "Password": "パスワード", "Confirm your current password before setting a new one.": "新しいパスワードを設定する前に現在のパスワードを確認してください。", "Account": "アカウント", "No longer want to use our service? You can delete your account here. This action is not reversible. All information related to this account will be deleted permanently.": "サービスの利用をやめたいですか？こちらでアカウントを削除できます。この操作は元に戻せません。このアカウントに関連するすべての情報が永久に削除されます。", "Delete account": "アカウントを削除", "Change Password": "パスワードを変更", "Security": "セキュリティ", "Credit Statistics": "クレジット統計", "enhance_prompt": "プロンプトを強化する", "Current Plan": "現在のプラン", "When you buy credits, you will be upgraded to Premium Plan.": "クレジットを購入すると、プレミアムプランにアップグレードされます。", "Available Credits": "利用可能なクレジット", "Purchased Credits": "購入したクレジット", "Plan Credits": "プランのクレジット", "profile.passwordChanged": "パスワードが変更されました", "profile.passwordChangedDescription": "パスワードが正常に変更されました。", "profile.passwordChangeError": "パスワードの変更に失敗しました。", "profile.passwordChangeErrorDescription": "パスワードの変更中にエラーが発生しました。もう一度試してください。", "delete": "削除", "profile.deleteAccount": "アカウントを削除", "profile.deleteAccountConfirmation": "アカウントを削除してもよろしいですか？この操作は元に戻すことができず、すべてのデータが完全に失われます。", "profile.accountDeleted": "アカウントが削除されました。", "profile.accountDeletedDescription": "あなたのアカウントは正常に削除されました。", "profile.accountDeletionError": "アカウントの削除に失敗しました。", "profile.accountDeletionErrorDescription": "アカウントの削除中にエラーが発生しました。もう一度お試しください。", "To celebrate our launch, enjoy 50% off select Gemini API models. Offer valid until further notice.": "私たちのローンチを記念して、特定のGemini APIモデルが50%オフになります。提供は追って通知があるまで有効です。", "Check now": "今すぐ確認してください", "payment.success.title": "支払いが成功しました。", "payment.success.message": "ご購入いただきありがとうございます！お支払い手続きが正常に完了しました。", "payment.success.orderId": "注文ID:", "payment.success.redirecting": "{seconds}秒後にご注文へリダイレクトします...", "payment.success.viewOrders": "注文を表示", "payment.error.title": "支払いエラー", "payment.error.message": "お支払いの処理中に問題が発生しました。これが続く場合はサポートにお問い合わせください。", "payment.error.backToOrders": "注文に戻る", "Overview of your credits status.": "クレジット状況の概要。", "Payment History": "支払い履歴", "Your payment history will appear here once you have made a purchase.": "購入が完了すると、ここに支払い履歴が表示されます。", "Payment method": "支払方法", "Purchase Date": "購入日", "Amount": "量", "Status": "ステータス", "Payment amount": "支払額", "payment.status.unavailable": "利用できません。", "payment.status.created": "作成された", "payment.status.completed": "完了", "payment.status.failed": "失敗しました", "payment.status.canceled": "キャンセルされた", "payment.status.processing": "処理", "payment.status.refund": "払い戻し", "payment.status.partial_paid": "部分支払い", "apiKeys.successTitle": "APIキーが正常に作成されました。", "apiKeys.importantNotice": "重要なお知らせ", "apiKeys.copyWarning": "これがこのAPIキーを表示してコピーできる唯一の機会です。今すぐコピーして、安全に保管してください。", "apiKeys.key": "APIキー", "apiKeys.securityTip": "セキュリティのヒント：", "apiKeys.tip1": "この鍵を安全な場所に保管してください。", "apiKeys.tip2": "APIキーを公に共有しないでください。", "apiKeys.tip3": "侵害された場合、このキーを削除して新しいものを作成してください。", "apiKeys.copyFirst": "まずAPIキーをコピーしてください。", "common.done": "完了", "Integration": "統合", "API Keys": "APIキー", "Manage your API keys.": "APIキーを管理します。", "Create API Key": "APIキーを作成", "Name your API key.": "あなたのAPIキーに名前を付けてください。", "Create": "作成する", "You have not created any API keys yet.": "あなたはまだAPIキーを作成していません。", "Copy API Key": "APIキーをコピー", "Delete API Key": "APIキーを削除", "EMAIL_NOT_EXIST": "メールは存在しません。", "Your account is not verified": "あなたのアカウントは確認されていません。", "Your account is not verified. Please verify your account to continue": "あなたのアカウントは確認されていません。続行するにはアカウントを確認してください。", "TOKEN_USED": "すでにトークンが使用されています", "NOT_ENOUGH_CREDIT": "クレジットが不足しています。アカウントにチャージしてください。", "Not enough credit": "クレジットが足りない", "Your account does not have enough credit. Please top up your account to continue.": "お客様のアカウントには十分なクレジットがありません。続行するには、アカウントにチャージしてください。", "{n} credits": "{n} クレジット", "USD / {unit}": "USD / {unit}", "Credits / {unit}": "クレジット / {unit}", "Save {n}%": "{n}%節約", "${price} = {n} credits": "${price} = {n}クレジット", "You can switch between money and credits to see the price in your preferred currency.": "お好みの通貨で価格を確認するために、マネーとクレジットを切り替えることができます。", "Forever": "永遠", "For large organizations.": "大規模組織向けに。", "Free": "無料", "Contact us": "お問い合わせ", "Contact sales": "営業に連絡する", "Premium": "プレミアム", "Enterprise": "エンタープライズ", "Show money": "ショー・マネー", "Show credits": "クレジットを表示", "Auto upgrade after buy credits": "購入後の自動アップグレード", "Image": "画像", "Video": "ビデオ", "Audio": "オーディオ", "Dialog": "ダイアログ", "Get started": "始める", "Contact": "連絡", "Image Style": "画像スタイル", "Image Aspect Ratio": "画像アスペクト比", "Enhance Prompt": "プロンプトを強化", "Aspect Ratio": "アスペクト比", "Support multiple aspect ratio": "複数のアスペクト比をサポートする。", "Support enhance prompt": "サポートを強化するプロンプト", "Up to {size}MB": "最大{size}MBまで", "Budget Calculator": "予算計算機", "Resource Calculator": "リソース計算機", "Budget Amount": "予算額", "Resources you can generate:": "生成できるリソース:", "Select resources you want:": "選択したいリソースを選んでください。", "credits": "クレジット", "image": "画像", "video": "ビデオ", "per item": "アイテムごとに", "Quantity": "数量", "Total Cost:": "合計費用:", "Approximately {credits} credits": "約{credits}クレジット", "Images": "画像", "Videos": "ビデオ", "Pricing Calculator": "料金計算機", "Calculate how many resources can you generate with your budget.": "予算でどれだけのリソースを生成できるか計算してください。", "Minimum $10 required": "最低 $10 が必要です。", "Minimum Purchase Required": "最低購入額が必要です。", "Minimum purchase amount is $10. Please increase your selection.": "最低購入金額は10ドルです。選択を増やしてください。", "Minimum purchase amount is $10": "最低購入金額は10ドルです。", "Please add more resources to reach the minimum purchase amount.": "最低購入金額に達するために、もっと多くのリソースを追加してください。", "Enter exact number": "正確な数字を入力してください。", "Enter budget amount": "予算額を入力してください", "Min: $10": "最低：$10", "Each amount shows what you can generate with your entire budget (choose one type)": "各金額は、予算全体で生成できるものを示しています（1つのタイプを選択してください）", "OR": "または", "AI Image Generation Examples": "AI画像生成の例", "Explore the power of AI image generation with these interactive comparisons": "これらのインタラクティブな比較でAI画像生成の力を探求しましょう。", "Try the Comparison!": "比較を試してみてください！", "Drag the slider left and right to compare the before and after images. You can also click anywhere on the image to move the slider.": "スライダーを左右にドラッグして、ビフォーアフターの画像を比較します。画像上をどこでもクリックしてスライダーを動かすこともできます。", "Got it!": "了解しました！", "Please login to access your saved prompts.": "保存されたプロンプトにアクセスするにはログインしてください。", "Access Your Personal Voices": "個人の声にアクセスする", "Access Your Favorite Voices": "お気に入りの声にアクセスする", "Login to view and manage your personal voice collection. Upload custom voices and access them anytime.": "ログインして個人の音声コレクションを閲覧および管理します。カスタム音声をアップロードして、いつでもアクセスできます。", "Login to view your favorite voices. Save voices you love and access them quickly for your projects.": "ログインしてお気に入りの音声を表示します。お気に入りの音声を保存してプロジェクト用にすばやくアクセスしましょう。", "Create Account": "アカウントを作成", "Join thousands of creators using AI voices for their projects": "何千ものクリエイターがプロジェクトにAIボイスを使用しています。", "AI Video Generation Examples": "AIビデオ生成例", "Explore the power of AI video generation with these interactive comparisons": "これらのインタラクティブな比較でAIビデオ生成の力を探求しよう。", "Try the Video Comparison!": "ビデオ比較を試してみてください！", "Drag the slider left and right to compare text prompts/images with generated videos. You can also click anywhere to move the slider.": "スライダーを左右にドラッグして、テキストプロンプトや画像と生成されたビデオを比較します。スライダーを動かすには、どこでもクリックすることもできます。", "Duration": "期間", "Select video duration in seconds": "ビデオの長さを秒で選択", "This setting is locked for the selected model": "この設定は選択されたモデルに対してロックされています。", "Prompts will always be refined to improve output quality (required for this model)": "プロンプトは常に洗練され、出力品質を向上させるために改善されます（このモデルに必要です）", "SIGNUP_MAIL_EXIST": "メールはすでに存在します。", "SIGNIN_USER_NOT_FOUND": "ユーザーが見つかりません", "SIGNIN_USER_NOT_VERIFIED": "ユーザーは確認されていません。", "SIGNIN_USER_DISABLED": "ユーザーが無効になっています。", "SIGNIN_WRONG_PASSWORD": "間違ったパスワード", "SIGNIN_USER_NOT_FOUND_FOR_EMAIL": "メールのユーザーが見つかりません。", "SIGNIN_INVALID_EMAIL": "無効なメールアドレス", "auth.accountCreated": "アカウントが作成されました。", "auth.accountCreatedDescription": "あなたのアカウントが正常に作成されました。アカウントを確認するため、メールをチェックしてください。", "Select voice on right": "右の音声を選択", "privacy.lastUpdated": "最終更新：", "privacy.lastUpdatedDate": "2025年1月15日", "privacy.introduction": "ジェミニジェン.AIでは、お客様のプライバシーの保護と個人情報のセキュリティを最優先に考えています。このプライバシーポリシーは、画像生成、ビデオ生成、音声合成、対話生成を含むAIコンテンツ生成サービスの利用時にお客様が提供する情報をどのように収集、利用、保護するかを概説しています。当社のウェブサイト（geminigen.ai）にアクセスし、利用することで、このポリシーに記載されている方法に同意したものとみなされます。", "privacy.informationCollectionDescription": "あなたが私たちのウェブサイトでアカウントを作成する際には、メールアドレスや氏名といった特定の個人情報を収集します。これらの情報は、サービスへのアクセスを提供するため、サービスの更新や変更を提供するため、及びオファリングを向上させるための統計分析に必要です。さらに、AIコンテンツ生成のためにアップロードされたテキスト、画像、またはドキュメントは、出力を生成する目的のみで一時的に保存されます。", "privacy.creditCalculation": "2. クレジット計算", "privacy.creditCalculationDescription": "正確な請求を保証するために、AIコンテンツ生成に必要なクレジット数は、提供されたテキスト、画像、またはドキュメントに基づいて計算されます。この計算は当社独自のアルゴリズムを使用して行われ、入力の複雑さと長さに直接比例しています。", "privacy.paymentSecurity": "3. 支払いとセキュリティ", "privacy.paymentSecurityDescription": "支払い処理には、PayPalとクレジットカードのオプションを提供しています。クレジットカード情報はサーバーに保存しません。すべての支払い取引は、信頼できる第三者の支払いサービスプロバイダーによって、各々のプライバシーとセキュリティポリシーに従って安全に処理されます。", "privacy.emailNotification": "4. メール通知と生成されたコンテンツへのアクセス", "privacy.emailNotificationDescription": "コンテンツの生成が完了すると、生成された出力物（画像、動画、音声ファイル、または対話）にアクセスしてダウンロードするための安全なリンクを含むメール通知を受け取ります。このリンクは、便宜上、指定された期間アクティブなままです。", "privacy.thirdPartyServices": "6. サードパーティサービス", "privacy.thirdPartyServicesDescription": "私たちは、サービスを向上させ、使用パターンを分析するために、アナリティクス提供者などのサードパーティサービスを利用する場合があります。これらのサービスは、使用状況に関する情報を収集することがありますが、個人情報にはアクセスできません。", "privacy.cookies": "クッキーと追跡技術", "privacy.cookiesDescription": "当社のウェブサイトは、ユーザーエクスペリエンスを向上させ、ウェブサイトの利用状況を分析するために、クッキーおよび類似の追跡技術を使用しています。ブラウザの設定でクッキーを無効にすることもできますが、その結果、当社のウェブサイトの一部の機能が正常に動作しない場合がありますのでご注意ください。", "privacy.thirdPartyLinks": "8. サードパーティリンク", "privacy.thirdPartyLinksDescription": "私たちのウェブサイトには、第三者のウェブサイトへのリンクが含まれている場合があります。これらのウェブサイトのプライバシー慣行やコンテンツについて当社は責任を負わず、それぞれのプライバシーポリシーをご確認いただくことをお勧めします。", "privacy.childrenPrivacy": "9. 子供のプライバシー", "privacy.childrenPrivacyDescription": "私たちのサービスは18歳未満の個人を対象としておらず、18歳未満の個人から個人情報を意図的に収集または保存することはありません。18歳未満の子供からの個人情報が意図せず収集されたことが判明した場合、当社はその情報を記録から削除するための手続きを行います。", "privacy.policyChanges": "プライバシーポリシーの更新", "privacy.policyChangesDescription": "私たちは、慣行や法的要件の変更を反映するためにプライバシーポリシーを定期的に更新することがあります。 改定されたポリシーは、当社のウェブサイトに掲載され次第、直ちに有効となります。 最新情報を得るために、このプライバシーポリシーを定期的にご確認いただくことをお勧めします。", "privacy.commercialUse": "11. 商業利用", "privacy.commercialUseDescription": "あなたは、私たちのサービスによって生成されたコンテンツを個人、教育、または商業目的で使用する権利を持っています。ただし、GeminiGen.AIからの事前の書面による同意なしに生成されたコンテンツを再販、再配布、またはサブライセンスすることはできません。", "privacy.otherPeoplePrivacy": "他人のプライバシー", "privacy.otherPeoplePrivacyDescription": "当社のサービスを使用する際には、他者のプライバシーを尊重しなければなりません。許可なく個人情報、機密データ、または著作権で保護された資料を含むコンテンツをアップロードまたは作成しないでください。", "privacy.unsubscribe": "13. 退会", "privacy.unsubscribeDescription": "プロファイル設定の「トグル」ボタンをクリックすることで、ターゲット広告をオプトアウトできます。", "terms.lastUpdated": "最終更新:", "terms.lastUpdatedDate": "2025年1月15日", "terms.introduction": "ジェミニジェン.AIへようこそ。これらの利用規約は、画像生成、ビデオ生成、音声合成、対話生成を含む、当社のAIを活用したコンテンツ生成サービスのご利用を規律するものです。", "terms.acceptanceOfTermsDetails": "当サービスを引き続き利用される場合は、これらの利用規約の変更を承諾されたものとみなされます。", "terms.serviceDescription": "2. サービスの説明", "terms.serviceDescriptionText": "GeminiGen.AIは、AIを活用したコンテンツ生成サービスを提供しています。その中には、次のものが含まれますが、それに限定されません。", "terms.serviceUsageDescription": "あなたは我々のサービスを合法的な目的のみに使用することを約束します。違法、有害、中傷、または他者の権利を侵害するコンテンツをアップロード、送信、または保存することを控える必要があります。AIコンテンツ生成のために提出されたあらゆるコンテンツについて、あなたが単独で責任を負います。", "terms.permittedUse": "4.1 許可された使用", "terms.permitted1": "当社のサービスを法的でクリエイティブ、商業的な目的にご利用ください。", "terms.permitted2": "適用される法律および規制に準拠したコンテンツを作成します。", "terms.permitted3": "他人の知的財産権を尊重する", "terms.permitted4": "生成されたコンテンツは、当社のライセンス条項に従って使用してください。", "terms.prohibitedUse": "4.2 禁止事項", "terms.prohibited1": "違法、有害、脅迫的、虐待的、または差別的な内容を生成する。", "terms.prohibited2": "他人の知的財産権を侵害するコンテンツを作成する", "terms.prohibited3": "他人を騙す、欺く、または誤解させることを目的としたコンテンツを作成する。", "terms.prohibited4": "未成年者を不適切な状況で描写するコンテンツを生成しないでください。", "terms.prohibited5": "暴力、テロリズム、または違法行為を促進するコンテンツを作成する。", "terms.prohibited6": "私たちのサービスを使用してスパムを送信したり、嫌がらせをしたり、他人に害を与えたりしないでください。", "terms.prohibited7": "我々のシステムをリバースエンジニアリングしたり、ハッキングしたり、侵害したりする試み", "terms.prohibited8": "適用される法律や規則に違反する", "terms.userAccounts1": "正確で完全な登録情報の提供", "terms.userAccounts2": "アカウント認証情報のセキュリティと機密性の維持", "terms.userAccounts3": "あなたのアカウントで発生するすべての活動", "terms.userAccounts4": "お客様のアカウントの不正使用をただちに通知してください。", "terms.userAccounts5": "あなたのアカウント情報が最新で正確であることを保証すること", "terms.paymentAndBilling": "3. 支払いとクレジット", "terms.paymentAndBillingDescription": "私たちのAIコンテンツ生成サービスは、クレジット制に基づいて運営されています。コンテンツ作成に必要なクレジット数は、入力の複雑さや出力の要件に基づいて独自のアルゴリズムにより正確に計算されます。", "terms.payment1": "クレジット残高がなくなった場合、アカウントにチャージする必要があります。", "terms.payment2": "支払いはPayPalまたはクレジットカードで行えます。", "terms.payment3": "すべての支払いは、サードパーティの支払い処理業者を通じて安全に処理されます。", "terms.payment4": "クレジットは適用される法律により要求されない限り、返金不可です。", "terms.payment5": "価格は、事前の適切な通知をもって変更される場合があります。", "terms.payment6": "あなたはすべての適用される税金と手数料を負担する責任があります。", "terms.ourIntellectualProperty": "5.1 当社の知的財産", "terms.ourIntellectualPropertyDescription": "GeminiGen.AIおよびそのサービスには、すべてのソフトウェア、アルゴリズム、デザイン、コンテンツが含まれており、知的財産法によって保護されています。当社の明確な書面による許可なしに、コピー、修正、配布、または派生作品を作成することはできません。", "terms.userGeneratedContent": "5.2 ユーザー生成コンテンツ", "terms.userGeneratedContentDescription": "お客様は、次の条件に従って、当社のサービスを使用して作成したコンテンツの所有権を保持します。", "terms.userContent1": "あなたは、私たちのサービスを提供するために、あなたのコンテンツを処理および保存する限定的なライセンスを私たちに許可します。", "terms.userContent2": "あなたは提供する入力コンテンツを使用する権利を有していることを表明します。", "terms.userContent3": "あなたは、生成したコンテンツがこれらの条件に準拠していることを確認する責任があります。", "terms.userContent4": "私たちは、ポリシーまたは適用される法律に違反するコンテンツを削除する場合があります。", "terms.privacyAndDataProtection": "7. プライバシーとデータ保護", "terms.privacyAndDataProtectionDescription": "お客様のプライバシーは私たちにとって重要です。お客様の個人情報の収集、使用、保護は、参照によってこれらの利用規約に組み込まれた私たちのプライバシーポリシーによって管理されています。", "terms.serviceAvailability": "8. サービスの利用可能性", "terms.serviceAvailabilityDescription": "信頼性のあるサービスを提供するよう努めていますが、継続的なアクセスを保証するものではありません。当社のサービスは、保守、更新、または技術的な問題により一時的に利用できない場合があります。合理的な通知をもって、サービスを変更または中止する権利を有します。", "terms.terminationByUser": "8.1 あなたによる契約解除", "terms.terminationByUserDescription": "アカウントを終了したい場合は、いつでもサポートチームにご連絡ください。終了後、サービスへのアクセスは停止しますが、これらの利用規約は以前のサービス利用に引き続き適用されます。", "terms.terminationByUs": "第8.2条 当社による終了", "terms.terminationByUsDescription": "次のいずれかの理由により、予告の有無にかかわらず、直ちにお客様のアカウントおよびサービスへのアクセスを停止または終了する場合があります。", "terms.termination1": "これらの利用規約の違反", "terms.termination2": "詐欺、不正行為、または違法行為", "terms.termination3": "料金または手数料の未払い", "terms.termination4": "長時間の非活動", "terms.termination5": "法律または規制の要件", "terms.termination6": "私たちの権利、財産、安全の保護", "terms.limitationOfLiability": "責任制限", "terms.limitationOfLiabilityDescription": "GeminiGen.AIは、弊社のサービスの使用に起因または関連するいかなる直接的、間接的、偶発的、特別または結果的な損害についても責任を負いません。サービスの正確性、完全性、または利用可能性を保証するものではなく、その使用または結果に関するすべての保証を明示的または黙示的に否認します。", "terms.disclaimer1": "商品性、特定の目的への適合性、および非侵害の保証", "terms.disclaimer2": "生成されたコンテンツの正確性、信頼性、または品質に関する保証", "terms.disclaimer3": "生成されたコンテンツの使用または配布に対する責任", "terms.disclaimer4": "サービスの中断や技術的な問題に起因するいかなる損害に対する責任", "terms.indemnification": "12. 補償", "terms.indemnificationDescription": "あなたは、当社のサービスの利用、これらの利用規約の違反、または第三者の権利の侵害に起因するいかなる請求、損害、損失、または費用についても、GeminiGen.AIおよびその関連会社を補償し、防御し、無害に保つことに同意します。", "terms.governingLaw": "9. 準拠法", "terms.governingLawDescription": "これらの利用規約は、法の抵触原則に関係なく、ベトナムの法律に従って解釈され、規定されます。これらの規約および当社のサービスの利用に起因または関連する紛争は、ベトナムの裁判所の専属管轄に従うものとします。", "terms.clarificationOpenAI": "第三者AIサービスに関する説明", "terms.clarificationOpenAIDescription": "GeminiGen.AIは独立した存在であり、OpenAI、Google、その他のAIサービスプロバイダーとは提携していません。当社のコンテンツ生成サービスは、さまざまなAI APIを利用してテキストを画像、動画、音声、対話に変換していますが、これらのプロバイダーとは独立して運営しています。これは、GeminiGen.AIと第三者のAIサービスプロバイダーとの関係に関する誤解や混乱を防ぐための明確化です。当社のサービスを提供するにあたり第三者のAI技術を使用していますが、GeminiGen.AIはこれらのサービスの運用および本サービス利用規約の順守に関する責任を単独で負っています。", "terms.contactEmail": "メール：", "terms.contactAddress": "住所:", "terms.companyAddress": "ウェブサイトはA2ZAI LTD No:16078579により共同運営されており、登録住所は483 Green Lanes, London, England, N13 4BSです", "footer.supportEmail": "メールサポート", "footer.address": "住所", "footer.companyAddress": "ウェブサイトはA2ZAI LTD No:16078579により共同運営されており、登録住所は483 Green Lanes, London, England, N13 4BSです", "second": "セカンド", "1M characters": "100万文字", "Support {n}+ voices": "{n}+の声をサポートする", "Support {n}+ emotions": "{n}+の感情をサポート", "Support {n}+ languages": "{n}+言語をサポート", "Support custom prompt": "カスタムプロンプトのサポート", "Support MP3 and WAV": "MP3とWAVをサポート", "Support speed control": "速度制御をサポート", "Support document to speech": "スピーチのためのサポート文書", "Current plan": "現行計画", "Already premium": "すでにプレミアム", "Gemini 2.5 Flash": "ジェミニ2.5フラッシュ", "Gemini 2.5 Pro": "ジェミニ2.5プロ", "Voice": "声", "Emotion": "感情", "Language": "言語", "Output Format": "出力形式", "Speed": "スピード", "Document": "ドキュメント", "Enjoy 50% off select Gemini API models. Offer valid until further notice.": "一部のGemini APIモデルが50％オフでお楽しみいただけます。オファーの有効期限は未定です。", "Text must be at least 4 characters long.": "テキストは少なくとも4文字以上である必要があります。", "Each dialog text must be at least 4 characters long.": "各ダイアログテキストは少なくとも4文字以上でなければなりません。", "Please enter text or select a file to generate speech.": "テキストを入力するか、ファイルを選択して音声を生成してください。", "Please select a voice for speech generation.": "音声生成のためのボイスを選択してください。", "Please add at least one dialog to generate speech.": "少なくとも1つのダイアログを追加して音声を生成してください。", "Please select voices for both speakers.": "両方の話者の声を選択してください。", "Photorealistic": "フォトリアリスティック", "imageStyles.photorealistic.description": "高詳細で高解像度のリアルな画像", "Delete Voice": "ボイスを削除", "Are you sure you want to delete this voice? This action cannot be undone.": "このボイスを削除してもよろしいですか？この操作は元に戻せません。", "Voice deleted successfully": "ボイスが正常に削除されました。", "Failed to delete voice": "音声の削除に失敗しました。", "Create your first voice": "最初のボイスを作成してください。", "Create Custom Voice": "カスタム音声を作成する", "Type of voice to create": "作成する声の種類", "Instant Voice Cloning": "瞬時の音声クローン作成", "Clone a voice from a clean sample recording. Samples should contain 1 speaker and be over 1 minute long and not contain background noise": "クリーンなサンプル録音から声をクローンします。サンプルには1人の話者が含まれ、1分以上の長さで、背景雑音が含まれていない必要があります。", "Professional Voice Cloning": "プロフェッショナルな音声クローン作成", "Create the most realistic digital replica of your voice.": "あなたの声の最もリアルなデジタルレプリカを作成します。", "Speaker Name": "話者名", "Enter speaker name": "話者の名前を入力してください。", "Describe the voice characteristics": "声の特徴を説明してください。", "Select gender": "性別を選択", "Select age": "年齢を選択", "Select accent": "アクセントを選択", "Audio Sample": "オーディオサンプル", "I agree to the privacy policy": "プライバシーポリシーに同意します。", "Note:": "注:", "The sound should be clear, without any noise, and last around 1 minutes to ensure good quality.": "音声はノイズがなく明瞭で、良質を確保するために約1分間続くべきです。", "It will cost 0 credits each time you create a voice.": "ボイスを作成するたびに0クレジットかかります。", "Young": "若い", "Middle": "中央", "Old": "古い", "English": "英語", "Custom voice created successfully": "カスタム音声が正常に作成されました。", "Failed to create custom voice": "カスタムボイスの作成に失敗しました。", "validation.mustAgreeToPrivacy": "プライバシーポリシーに同意しなければなりません。", "I agree to the {0}": "私は{0}に同意します", "Privacy Policy": "プライバシーポリシー", "The sound should be clear, without any noise, and last around 10 minutes to ensure good quality.": "音はノイズがなく明瞭で、良質を確保するために約10分続くべきです。", "It will cost 5,000 credits each time you create a voice.": "音声を作成するたびに5,000クレジットかかります。", "Maximum file size": "最大ファイルサイズ", "File size exceeds maximum limit of {maxSize}": "ファイルサイズが最大限度の{maxSize}を超えています。", "File size exceeds 150MB limit": "ファイルサイズが150MBの制限を超えています。", "ui.errors.viewGooglePolicy": "Google の生成 AI 使用ポリシーを閲覧する", "negativePrompt": "Negative Prompt", "negativePromptDescription": "Describe what you don't want to see in the video", "negativePromptTooltip": "Negative prompts help exclude unwanted elements, styles, or concepts from your video generation. For example: 'blurry, low quality, distorted faces'", "negativePromptPlaceholder": "Enter what you want to avoid in the video...", "negativePromptSuggestions": "Quick suggestions", "negativePromptSuggestion1": "blurry, low quality", "negativePromptSuggestion2": "distorted faces, deformed", "negativePromptSuggestion3": "text, watermark", "negativePromptSuggestion4": "dark, underexposed", "negativePromptSuggestion5": "shaky, unstable motion", "negativePromptSuggestion6": "pixelated, artifacts", "negative_prompt": "ネガティブプロンプト", "aspect_ratio": "アスペクト比", "person_generation": "パーソン・ジェネレーション", "ALLOW_ADULT": "大人を許可する", "ALLOW_ALL": "すべてを許可する", "veo-3-fast": "ヴェオ3ファスト", "Click to copy. UUID is unique and can be used to contact support.": "クリックしてコピー。UUIDは一意であり、サポートへの連絡に使用できます。", "Account Activation Required": "アカウントの有効化が必要です。", "Please activate your account to access your personal voice collection. Check your email for the activation link.": "アカウントを有効化して、パーソナルボイスコレクションにアクセスしてください。認証リンクが記載されたメールを確認してください。", "Please activate your account to access your favorite voices. Check your email for the activation link.": "アカウントを有効化してお気に入りの声にアクセスしてください。メールで送られた有効化リンクを確認してください。", "Resend Activation Email": "再送信アクティベーションメール", "Check Email": "メールを確認する", "Didn't receive the email? Check your spam folder or try resending.": "メールが届きませんでしたか？迷惑メールフォルダを確認するか、再送信してみてください。", "Activation email sent": "アクティベーションメールを送信しました。", "Please check your email for the activation link.": "メールで送られてきたアクティベーションリンクを確認してください。", "Voice Training Started": "ボイストレーニングが始まりました。", "Your custom voice is being trained. You will be notified when it's ready.": "カスタム音声がトレーニングされています。準備が整いましたら通知いたします。", "Voice Training Complete": "ボイストレーニング完了", "Your custom voice is ready to use!": "カスタムボイスの準備が整いました！", "Voice Training Failed": "ボイストレーニングに失敗しました。", "Voice training failed. Please try again.": "音声トレーニングに失敗しました。もう一度お試しください。", "Training": "トレーニング", "Failed": "失敗しました", "Voice Training in Progress": "音声トレーニング進行中", "Your custom voice is being trained. This process may take up to 30 minutes. You will be notified when it's ready.": "あなたのカスタムボイスがトレーニング中です。このプロセスは最大30分かかる場合があります。準備が整いましたら通知いたします。", "Voice Name": "ボイスネーム", "Training Type": "トレーニングタイプ", "Training Progress": "トレーニングの進捗", "Estimated time: 5-30 minutes": "推定時間：5〜30分", "Refresh Status": "ステータスの更新", "Voice Not Available": "音声は利用できません。", "This voice is currently being trained and cannot be selected yet.": "この音声は現在トレーニング中で、まだ選択できません。", "hero.title": "素晴らしいAIコンテンツを作成する", "hero.subtitle": "あなたのアイデアを魅力的なAI生成の画像、動画、音声、その他に変換します。創造的なコンテンツ生成の未来を体験してください。", "hero.cta.primary": "作成を開始", "hero.cta.secondary": "デモを見る", "hero.users": "世界中の1万人以上のクリエイターに信頼されています", "hero.scroll": "スクロールして探索する", "features.title": "主な機能", "features.subtitle": "プロフェッショナルなAIコンテンツを作成するための強力な機能を発見してください。", "features.ai.title": "先進的なAI", "features.ai.description": "最先端のAI技術を使用して、素晴らしい精度で高品質なコンテンツを生成します。", "features.speed.title": "高速生成", "features.speed.description": "あなたのアイデアをわずか数秒でコンテンツに変換します。長い待ち時間はありません。", "features.creative.title": "無限の創造性", "features.creative.description": "アニメーションからリアル、芸術的からプロフェッショナルまで、あらゆるスタイルでコンテンツを作成します。", "features.quality.title": "高品質", "features.quality.description": "高解像度で滑らかな動きと鮮明なディテールを備えた出力。", "features.collaboration.title": "簡単なコラボレーション", "features.collaboration.description": "プロジェクトをチームと簡単に共有し、協力しましょう。", "features.export.title": "マルチフォーマットエクスポート", "features.export.description": "さまざまなプラットフォームと目的に適した形式でコンテンツをエクスポートする。", "features.cta.title": "始める準備はできましたか？", "features.cta.description": "今日、コンテンツ作成におけるAIの力を体験しよう。", "features.cta.button": "最初のコンテンツを作成する", "howto.title": "AIコンテンツの作り方", "howto.subtitle": "わずか4つの簡単なステップで、プロフェッショナルなAIコンテンツを作成できます。", "howto.step1.title": "アイデアを説明してください。", "howto.step1.description": "作成したいコンテンツの詳細な説明を書いてください。シーン、キャラクター、アクション、スタイルを含めてください。", "howto.step2.title": "設定をカスタマイズする", "howto.step2.description": "目的に適した解像度、アスペクト比、スタイル、およびその他のパラメータを選択してください。", "howto.step3.title": "AI処理", "howto.step3.description": "人工知能があなたのリクエストを分析し、数秒で高品質なコンテンツを作成します。", "howto.step4.title": "ダウンロードして共有", "howto.step4.description": "コンテンツをコンピュータにダウンロードするか、お気に入りのソーシャルメディアプラットフォームに直接共有してください。", "howto.examples.title": "AIコンテンツの例", "howto.examples.subtitle": "シンプルなプロンプトから作成されたコンテンツを参照してください。", "howto.cta": "今すぐ作成してみてください", "faq.title": "よくある質問", "faq.description": "AIコンテンツ作成に関する一般的な質問への答えを見つける", "faq.general.title": "一般", "faq.services.title": "サービス", "faq.pricing.title": "価格設定", "faq.contact.title": "まだ質問がありますか？", "faq.contact.description": "私たちのサポートチームは24時間365日体制でお手伝いいたします。", "faq.contact.email": "メールを送る", "faq.contact.discord": "ディスコードに参加する", "footer.product.title": "製品", "footer.product.features": "機能", "footer.product.howto": "使い方", "footer.product.pricing": "価格設定", "footer.product.app": "アプリ", "footer.company.title": "会社", "footer.company.about": "私たちについて", "footer.company.blog": "ブログ", "footer.company.careers": "キャリア", "footer.company.contact": "連絡先", "footer.legal.title": "法務", "footer.legal.privacy": "プライバシーポリシー", "footer.legal.terms": "利用規約", "footer.legal.cookies": "クッキーポリシー", "footer.support.title": "サポート", "footer.support.help": "ヘルプセンター", "footer.support.api": "APIドキュメント", "footer.support.status": "システムステータス", "footer.description": "最先端の技術で高品質なAIコンテンツを作成します。アイデアを瞬時にプロフェッショナルなコンテンツに変換します。", "footer.newsletter.title": "最新情報を取得する", "footer.newsletter.description": "新機能や製品の更新情報を受け取るために登録してください。", "footer.newsletter.placeholder": "メールアドレスを入力してください。", "footer.newsletter.subscribe": "登録する", "footer.copyright": "© 2024 GeminiGen AI。全著作権所有。", "footer.language": "英語", "Features That Set Us Apart": "私たちを際立たせる特徴", "howto.examples.tryPrompt": "このプロンプトを試してみてください。", "howto.examples.example1.title": "桜の猫", "howto.examples.example1.prompt": "夕暮れの桜畑を走る猫", "howto.examples.example2.title": "オーシャンサーフィン", "howto.examples.example2.prompt": "青い海の波でサーフィンをする男が、美しい晴れた日にいる。", "howto.examples.example3.title": "未来都市", "howto.examples.example3.prompt": "空飛ぶ車とそびえ立つ超高層ビルのある未来都市", "howto.examples.example4.title": "文化舞踊", "howto.examples.example4.prompt": "カラフルな衣装を着た伝統的な踊り手たちが祭りで演技をする", "howto.examples.example5.title": "山の風景", "howto.examples.example5.prompt": "穏やかな山の風景、流れる川や霧に包まれた峰々", "videoGen.imageToVideo": "画像から動画へ", "videoGen.textToVideo": "テキストからビデオへ", "videoGen.generated": "生成された", "videoGen.imageToVideoGenerated": "画像から映像への生成", "videoGen.textToVideoGenerated": "テキストからビデオへの生成", "videoGen.generationStarted": "ビデオの生成を開始していますので、約2分ほどお待ちください。", "videoGen.imageToVideoGenerationStarted": "画像からビデオの生成を開始しています。約2分お待ちください。", "videoGen.textToVideoGenerationStarted": "テキストからビデオの生成を開始しています。約2分お待ちください。", "videoGen.pleaseEnterPrompt": "ビデオを作成するためのプロンプトを入力してください。", "videoGen.cinematic": "シネマティック", "videoGen.model": "モデル", "videoGen.imageReference": "画像参照", "videoGen.prompt": "プロンプト", "videoGen.negativePrompt": "ネガティブプロンプト", "videoGen.negativePromptDescription": "ビデオで見たくないものを説明してください。", "videoGen.negativePromptPlaceholder": "ビデオから除外したい内容を入力してください。", "videoGen.negativePromptTooltip": "ネガティブプロンプトは、ビデオ生成から不要な要素、スタイル、または概念を排除するのに役立ちます。例えば、「ぼやけた、低品質、歪んだ顔」などです。", "videoGen.negativePromptSuggestions": "迅速な提案", "videoGen.negativePromptSuggestion1": "ぼやけた、低品質", "videoGen.negativePromptSuggestion2": "歪んだ顔", "videoGen.negativePromptSuggestion3": "ひどい解剖学", "videoGen.negativePromptSuggestion4": "透かし、テキスト", "videoGen.negativePromptSuggestion5": "飽和状態の", "videoGen.negativePromptSuggestion6": "非現実的な動き", "videoGen.enhancePrompt": "プロンプトを強化する", "videoGen.enhancePromptOn": "プロンプトは常に出力品質を向上させるために洗練されます。", "videoGen.enhancePromptOnRequired": "プロンプトは常に改良されて出力の品質を向上させます（このモデルには必要です）", "videoGen.enhancePromptOff": "プロンプトは修正されません。", "videoGen.enhancePromptLocked": "この設定は選択したモデルに対してロックされています。", "videoGen.enhancePromptNotRefined": "プロンプトは洗練されません。", "videoGen.aspectRatio": "アスペクト比", "videoGen.duration": "期間", "videoGen.selectDuration": "ビデオの長さを秒で選択", "videoGen.creditsRemaining": "クレジット: 残り {credits}", "videoGen.generationCost": "この世代は{duration}秒間に{cost}クレジットがかかります。", "videoGen.generateVideo": "ビデオを生成する", "videoGen.somethingWentWrong": "問題が発生しました。", "videoGen.examplesTitle": "AI動画生成例", "videoGen.examplesDescription": "AI動画生成の力を体験できるインタラクティブな比較をご覧ください。", "videoGen.processing": "ビデオを処理しています...", "videoGen.analyzing": "入力を解析しています…", "videoGen.rendering": "ビデオをレンダリングしています…", "videoGen.finalizing": "ビデオの最終処理中...", "videoGen.almostReady": "あなたのビデオはほぼ準備が整いました･･･", "videoGen.estimatedTime": "推定時間：2〜3分", "videoGen.backgroundProcessing": "あなたのビデオはバックグラウンドで生成されています。", "videoGen.checkHistory": "履歴で進捗を確認できます。", "videoGen.willNotify": "準備が整いましたらお知らせいたします。", "videoGen.error.invalidPrompt": "有効なプロンプトを入力してください。", "videoGen.error.invalidModel": "有効なモデルを選択してください。", "videoGen.error.invalidDuration": "有効な期間を選択してください。", "videoGen.error.invalidAspectRatio": "有効なアスペクト比を選択してください。", "videoGen.error.insufficientCredits": "ビデオ生成のためのクレジットが不足しています。", "videoGen.error.fileTooLarge": "選択した画像ファイルが大きすぎます。", "videoGen.error.invalidFileFormat": "無効な画像ファイル形式", "videoGen.error.generationFailed": "ビデオの生成に失敗しました", "videoGen.error.networkError": "ビデオ生成中のネットワークエラー", "videoGen.error.serverError": "ビデオ生成中にサーバーエラーが発生しました。", "videoGen.success.generationStarted": "ビデオの生成が正常に開始されました。", "videoGen.success.generationCompleted": "ビデオの生成が正常に完了しました。", "videoGen.success.imageUploaded": "画像が正常にアップロードされました。", "videoGen.success.promptSaved": "プロンプトが正常に保存されました。", "Image Generation": "画像生成", "We are starting to generate your image, please wait about 1 minute...": "私たちはあなたの画像を生成し始めています。約1分お待ちください。", "We are starting to generate speech from your document, please check back later...": "私たちはあなたのドキュメントから音声の生成を開始しています。後ほどご確認ください。", "We are starting to generate your speech, please wait about {time} {unit}...": "スピーチの生成を開始しています。{time}{unit}ほどお待ちください...", "minutes": "分", "seconds": "秒", "faq.questions.q1.title": "なぜ他のTTVツールではなくgeminigen.aiを選ぶのか？", "faq.questions.q1.content": "Geminigen.aiは、市場に出回っている他のアプリケーションと比較して、より手頃な価格でテキストからAI生成の画像や動画を提供します。テキストからの画像や動画生成に加えて、テキスト読み上げサービスやテキストベースの会話生成も提供しています。", "faq.questions.q2.title": "geminigen.aiのサービスをどのように利用しますか？", "faq.questions.q2.content": "私たちのサービスはユーザーフレンドリーに設計されています。簡単に望むビデオをテキストで説明するだけで、システムが自動的にそれをビデオに変換します。", "faq.questions.q3.title": "geminigen.aiを使用するにはプログラミングの知識が必要ですか？", "faq.questions.q3.content": "いいえ、プログラミングの知識は必要ありません。私たちはGeminiのTTV APIをウェブサイトに統合し、テキストを画像や動画に変換するプロセスを誰にでも簡単で便利にしました。", "faq.questions.q4.title": "テキスト入力に対応している言語は何ですか？", "faq.questions.q4.content": "私たちは現在、英語、ベトナム語、スペイン語などを含む複数の言語をサポートしています。追加の言語のサポートも定期的に追加されています。", "faq.questions.q5.title": "geminigen.aiの利用料金はいくらですか？", "faq.questions.q5.content": "私たちはGeminiの価格設定に基づいて価格を設定しており、市場にある多くの他の音声合成ツールよりもコストを抑えています。詳細な価格については、こちらのページをご参照ください：https://geminigen.ai/pricing", "faq.questions.q6.title": "geminigen.aiを商業目的で使用できますか？", "faq.questions.q6.content": "はい、弊社のサービスは個人と商業の両方の目的に対応しています。ただし、利用規約への遵守をお願いします。", "faq.questions.q7.title": "geminigen.aiのビデオの品質はどうですか?", "faq.questions.q7.content": "生成された画像と動画は、ジェミニの先進的なTTV技術のおかげで、高品質でリアルで鮮やかなビジュアルを備えています。", "faq.questions.q8.title": "geminigen.aiで自分のプライバシーとデータを保護するにはどうすればいいですか。", "faq.questions.q8.content": "ユーザーのセキュリティとプライバシーは当社の最優先事項です。当社は高度なセキュリティ対策を講じてデータを保護し、あなたの同意なしに第三者と情報を共有することはありません。", "faq.questions.q9.title": "テキスト音声変換の場合、出力音声を編集またはカスタマイズできますか？", "faq.questions.q9.content": "プラットフォームでは直接編集機能を提供していませんが、変換前に読み上げ速度やトーンなどの設定をカスタマイズすることができます。これにより、出力ファイルの雰囲気や最終的な音をより良く管理することが可能です。", "faq.questions.q10.title": "テキスト読み上げ変換のために、追加の声や新しい言語をリクエストすることはできますか？", "faq.questions.q10.content": "私たちは常にユーザーのフィードバックに耳を傾け、サービスの拡大に努めています。特定の声や言語に関する具体的なリクエストがあれば、どうぞ気軽にサポートシステムにご提出ください。", "faq.questions.q11.title": "サービスの利用中に問題が発生した場合、どこに技術サポートに連絡すればよいですか？", "faq.questions.q11.content": "私たちは、メールとウェブサイト上のライブチャットでサポートを提供しています。サポートチームはいつでも質問にお答えし、必要な時にお手伝いします。", "faq.questions.q12.title": "サービスを利用するにはアカウントを作成する必要がありますか？", "faq.questions.q12.content": "はい、アカウントを作成することで、変換された画像、ビデオ、ドキュメントを簡単に管理し、高度な機能やより良いカスタマーサポートサービスにアクセスできます。", "faq.questions.q13.title": "私のウェブサイトやブログのコンテンツを作成するためにgeminigen.aiを使用できますか？", "faq.questions.q13.content": "はい、当社のサービスを利用して、ウェブサイト、ブログ、ソーシャルメディアプラットフォームのための音声コンテンツを作成し、読者や顧客への情報提供方法を充実させることができます。", "faq.questions.q14.title": "画像の出力ファイル形式は何ですか？", "faq.questions.q14.content": "主な出力ファイル形式はpngであり、ほとんどのデバイスとの互換性が保証されています。", "faq.questions.q15.title": "ビデオを作成するのにいくらかかりますか？", "faq.questions.q15.content": "作成する動画の秒数に基づいてクレジットを計算します。詳細な価格については、こちらのページをご参照ください：https://geminigen.ai/pricing", "faq.questions.q16.title": "音声の出力ファイル形式は何ですか？", "faq.questions.q16.content": "主な出力ファイル形式はMP3およびWAVであり、ほとんどのデバイスや音楽再生ソフトウェアと互換性があります。", "faq.questions.q17.title": "支払い手続きは難しいですか？利用可能な支払い方法は何ですか？", "faq.questions.q17.content": "支払いプロセスは非常に簡単です。私たちは、PayPal、デビット、暗号通貨を含む一般的な支払い方法を提供しています。", "faq.questions.q18.title": "頻繁に利用するユーザー向けのサブスクリプションプランはありますか？", "faq.questions.q18.content": "いいえ、私たちは有効期限のないクレジットを販売しています。クレジットがなくなったら、別のクレジットパックを購入してください。", "faq.questions.q19.title": "ビデオが失敗したりエラーが発生したりした場合、返金を受けられますか？", "faq.questions.q19.content": "ビデオが正常に作成された場合にのみクレジットをカウントします。エラーが発生した場合、クレジットはカウントされません。", "Transform your ideas into stunning AI-generated images, videos, speech, and more.": "あなたのアイデアを魅力的なAI生成の画像、ビデオ、音声などに変換しましょう。", "Save up to {0} compared to traditional creative services while experiencing the future of content generation.": "従来のクリエイティブサービスと比較して、コンテンツ生成の未来を体験しながら最大で{0}を節約できます。", "30 seconds": "30秒", "5 minutes": "5分", "Minimum required": "最小要件", "Audio duration must be at least {duration}": "オーディオの長さは少なくとも{duration}でなければなりません。", "The sound should be clear, without any noise, and last at least 5 minutes to ensure good quality.": "音はノイズがなく、明瞭であるべきで、5分以上持続して良好な品質を確保してください。", "The sound should be clear, without any noise, and last at least 30 seconds to ensure good quality.": "音声は明瞭でノイズがなく、良質を確保するために少なくとも30秒持続するべきです。"}